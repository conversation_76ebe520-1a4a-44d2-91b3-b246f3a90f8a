import { gql, useQuery } from '@apollo/client';
import { useMemo } from 'react';

import { transformNotification } from '#application/notifications/utils/NotificationTransformer';
import { Notification } from '#domain/types/Notification.Type';

interface UseGetNotificationsOptions {
  limit?: number;
  offset?: number;
  pollInterval?: number;
}

interface NotificationQueryResult {
  id: string;
  owner_user_id: string;
  company: {
    name: string;
  };
  payload: Record<string, unknown>;
  required_roles: string[];
  type: string;
  createdAt: string;
}

const GET_NOTIFICATIONS = gql`
  query GetNotifications($limit: Int, $offset: Int) {
    notification(
      order_by: { createdAt: desc }
      limit: $limit
      offset: $offset
    ) {
      id
      owner_user_id
      company {
        name
      }
      payload
      required_roles
      type
      createdAt
    }
  }
`;

export const useGetNotifications = (options: UseGetNotificationsOptions = {}) => {
  const {
    limit = 5,
    offset = 0,
    pollInterval = 30000,
  } = options;

  const {
    data, loading, error, refetch,
  } = useQuery<{
    notification: NotificationQueryResult[];
  }>(GET_NOTIFICATIONS, {
    variables: { limit, offset },
    pollInterval,
    errorPolicy: 'all',
    fetchPolicy: 'cache-and-network',
  });

  const notifications = useMemo(() => {
    if (!data?.notification) return [];

    return data.notification.map((notificationData): Notification => transformNotification({
      id: notificationData.id,
      owner_user_id: notificationData.owner_user_id,
      company: notificationData.company,
      company_id: notificationData.company?.name || '',
      payload: notificationData.payload,
      required_roles: notificationData.required_roles,
      type: notificationData.type,
      createdAt: new Date(notificationData.createdAt),
    }));
  }, [data]);

  return {
    notifications,
    loading,
    error,
    refetch,
  };
};

export const useGetUnreadNotifications = () => useGetNotifications({
  limit: 10,
  pollInterval: 15000,
});
