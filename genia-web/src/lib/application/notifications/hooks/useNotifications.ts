import {
  useCallback, useEffect, useMemo, useReducer,
} from 'react';

import { useGetNotifications } from '#application/notifications/hooks/useGetNotifications.Hook';
import { NotificationStorage } from '#application/notifications/utils/NotificationStorage';
import { Notification as NotificationType } from '#domain/types/Notification.Type';
import { usePagination } from '#infrastructure/implementation/application/hooks/usePagination.Hook';

// Tipos para el reducer
interface NotificationsState {
  accumulatedNotifications: NotificationType[];
  isLoadingMore: boolean;
}

type NotificationsAction =
  | { type: 'SET_INITIAL_NOTIFICATIONS'; payload: NotificationType[] }
  | { type: 'APPEND_NOTIFICATIONS'; payload: NotificationType[] }
  | { type: 'SET_LOADING_MORE'; payload: boolean }
  | { type: 'RESET_NOTIFICATIONS' };

// Reducer para manejar el estado de las notificaciones
const notificationsReducer = (state: NotificationsState, action: NotificationsAction): NotificationsState => {
  switch (action.type) {
    case 'SET_INITIAL_NOTIFICATIONS':
      return {
        ...state,
        accumulatedNotifications: action.payload,
        isLoadingMore: false,
      };
    case 'APPEND_NOTIFICATIONS':
      return {
        ...state,
        accumulatedNotifications: [...state.accumulatedNotifications, ...action.payload],
        isLoadingMore: false,
      };
    case 'SET_LOADING_MORE':
      return {
        ...state,
        isLoadingMore: action.payload,
      };
    case 'RESET_NOTIFICATIONS':
      return {
        accumulatedNotifications: [],
        isLoadingMore: false,
      };
    default:
      return state;
  }
};

export const useNotifications = () => {
  const itemsPerPage = 5;

  // Estado inicial del reducer
  const initialState: NotificationsState = {
    accumulatedNotifications: [],
    isLoadingMore: false,
  };

  const [state, dispatch] = useReducer(notificationsReducer, initialState);
  const { accumulatedNotifications, isLoadingMore } = state;

  const {
    currentPage, offset, handlePageChange,
  } = usePagination(itemsPerPage);

  const {
    notifications, loading, error, refetch,
  } = useGetNotifications({
    limit: itemsPerPage,
    offset,
  });

  const newNotificationsCount = useMemo(() => NotificationStorage.getNewNotificationsCount(accumulatedNotifications), [accumulatedNotifications]);

  // Acumular notificaciones para scroll infinito
  useEffect(() => {
    if (!notifications) return;

    if (offset === 0) {
      // Primera carga o refresh
      dispatch({ type: 'SET_INITIAL_NOTIFICATIONS', payload: notifications });
      return;
    }

    if (notifications.length === 0) {
      // No hay más notificaciones
      dispatch({ type: 'SET_LOADING_MORE', payload: false });
      return;
    }

    // Agregar nuevas notificaciones al final
    dispatch({ type: 'APPEND_NOTIFICATIONS', payload: notifications });
  }, [notifications, offset]);

  useEffect(() => {
    if (accumulatedNotifications && accumulatedNotifications.length > 0) {
      NotificationStorage.saveLastSeenNotificationId(accumulatedNotifications);
    }
  }, [accumulatedNotifications]);

  const loadMoreNotifications = useCallback(() => {
    if (isLoadingMore) return;
    if (!notifications || notifications.length < itemsPerPage) return; // No hay más datos

    dispatch({ type: 'SET_LOADING_MORE', payload: true });
    handlePageChange(currentPage + 1);
  }, [isLoadingMore, notifications, itemsPerPage, handlePageChange, currentPage]);

  const hasMore = useMemo(() => notifications && notifications.length === itemsPerPage, [notifications, itemsPerPage]);

  return {
    notifications: accumulatedNotifications,
    newNotificationsCount,
    loading,
    error,
    refetch: () => {
      dispatch({ type: 'RESET_NOTIFICATIONS' });
      handlePageChange(1);
      refetch();
    },
    loadMoreNotifications,
    hasMore: hasMore || false,
    isLoadingMore,
  };
};
