import { useQuery } from '@apollo/client';
import { useCallback } from 'react';

import { GET_NOTIFICATIONS } from '#application/notifications/queries/GetNotifications.Query';
import { mapNotification } from '#application/notifications/utils/NotificationMapper';

export const useNotifications = (options?: {
  limit?: number;
  offset?: number;
  onNewData?: (notifications: NotificationType[]) => void;
}) => {
  const { limit = 25, offset = 0, onNewData } = options || {};

  const {
    data, loading, error, fetchMore,
  } = useQuery(GET_NOTIFICATIONS, {
    variables: { limit, offset },
    pollInterval: 30000,
    errorPolicy: 'all',
    fetchPolicy: 'cache-and-network',
    onCompleted: (data) => {
      if (onNewData && data?.notification) {
        onNewData(data.notification.map(mapNotification));
      }
    },
  });

  const notifications = data?.notification?.map(mapNotification) || [];
  const hasMore = notifications.length === limit;

  const loadMore = useCallback(() => {
    if (hasMore && !loading) {
      fetchMore({
        variables: { offset: offset + limit },
        updateQuery: (prev, { fetchMoreResult }) => {
          if (!fetchMoreResult) return prev;
          return {
            ...fetchMoreResult,
            notification: [...prev.notification, ...fetchMoreResult.notification],
          };
        },
      });
    }
  }, [hasMore, loading, fetchMore, offset, limit]);

  return {
    notifications,
    loading,
    error,
    hasMore,
    loadMore,
    newNotificationsCount: 0, // Calcular según tu lógica
  };
};
