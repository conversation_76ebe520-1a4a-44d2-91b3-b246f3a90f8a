import {
  useCallback, useEffect, useMemo, useState,
} from 'react';

import { useGetNotifications } from '#application/notifications/hooks/useGetNotifications.Hook';
import { NotificationStorage } from '#application/notifications/utils/NotificationStorage';
import { Notification as NotificationType } from '#domain/types/Notification.Type';
import { usePagination } from '#infrastructure/implementation/application/hooks/usePagination.Hook';

export const useNotifications = () => {
  const itemsPerPage = 5;
  const [accumulatedNotifications, setAccumulatedNotifications] = useState<NotificationType[]>([]);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const {
    currentPage, offset, handlePageChange,
  } = usePagination(itemsPerPage);

  const {
    notifications, loading, error, refetch,
  } = useGetNotifications({
    limit: itemsPerPage,
    offset,
  });

  const newNotificationsCount = useMemo(() => NotificationStorage.getNewNotificationsCount(accumulatedNotifications), [accumulatedNotifications]);

  // Acumular notificaciones para scroll infinito
  useEffect(() => {
    if (!notifications) return;

    if (offset === 0) {
      // Primera carga o refresh
      setAccumulatedNotifications(notifications);
      setIsLoadingMore(false);
      return;
    }

    if (notifications.length === 0) {
      // No hay más notificaciones
      setIsLoadingMore(false);
      return;
    }

    // Agregar nuevas notificaciones al final
    setAccumulatedNotifications((prev) => [...prev, ...notifications]);
    setIsLoadingMore(false);
  }, [notifications, offset]);

  useEffect(() => {
    if (accumulatedNotifications && accumulatedNotifications.length > 0) {
      NotificationStorage.saveLastSeenNotificationId(accumulatedNotifications);
    }
  }, [accumulatedNotifications]);

  const loadMoreNotifications = useCallback(() => {
    if (isLoadingMore) return;
    if (!notifications || notifications.length < itemsPerPage) return; // No hay más datos

    setIsLoadingMore(true);
    handlePageChange(currentPage + 1);
  }, [isLoadingMore, notifications, itemsPerPage, handlePageChange, currentPage]);

  const hasMore = useMemo(() => notifications && notifications.length === itemsPerPage, [notifications, itemsPerPage]);

  return {
    notifications: accumulatedNotifications,
    newNotificationsCount,
    loading,
    error,
    refetch: () => {
      handlePageChange(1);
      refetch();
    },
    loadMoreNotifications,
    hasMore: hasMore || false,
    isLoadingMore,
  };
};
