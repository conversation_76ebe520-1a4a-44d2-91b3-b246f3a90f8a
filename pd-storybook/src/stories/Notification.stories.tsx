import type { <PERSON>a, StoryObj } from '@storybook/react';

import {
  StyledNotificationsMenuContainer,
  StyledNotificationsMenuMessages,
} from '../components/header/Header.Style';
import { NotificationComponent } from '../components/notification/Notification.Component';
import { DefaultNotificationMenu } from '../components/notification/NotificationMenu.Component';
import { Title } from '../components/title/Title.Component';

const meta = {
  title: 'Components/Notification',
  component: NotificationComponent,
  parameters: {
    layout: 'centered',
    backgrounds: {
      default: 'dark',
    },
  },
  argTypes: {
    notifications: {
      control: 'object',
    },
    badgeCount: {
      control: 'number',
    },
  },
} as Meta<typeof NotificationComponent>;

export default meta;

type Story = StoryObj<typeof NotificationComponent>;

export const Default: Story = {
  render: (args) => {
    const notifications = [
      {
        id: '1',
        message: 'Nueva orden #1234 ha sido creada por <PERSON>',
        isRead: false,
        createdAt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutos atras
      },
      {
        id: '2',
        message: 'Alerta de inventario: Producto "iPhone 15" tiene stock bajo (3 unidades)',
        isRead: false,
        createdAt: new Date(Date.now() - 15 * 60 * 1000), // 15 minutos atras
      },
      {
        id: '3',
        message: 'Pago recibido para orden #1230 - $1,299.99',
        isRead: false,
        createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutos atras
      },
    ];

    return (
      <NotificationComponent {...args}>
        <DefaultNotificationMenu
          notifications={notifications}
          visibleNotifications={notifications}
          remainingCount={0}
          showRemainingCount={false}
          isOpen={true}
        />
      </NotificationComponent>
    );
  },
  args: {
    badgeCount: 3,
  },
};

export const NoNotifications: Story = {
  args: {
    notifications: null,
    badgeCount: 0,
  },
};

export const EmptyNotifications: Story = {
  args: {
    notifications: [],
    badgeCount: 0,
  },
};

export const ManyNotifications: Story = {
  render: (args) => {
    const notifications = Array.from({ length: 15 }, (_, i) => ({
      id: `${i + 1}`,
      message: `Notification ${i + 1}`,
      isRead: false,
      createdAt: new Date(Date.now() - (i + 1) * 60 * 1000),
    }));

    return (
      <NotificationComponent {...args}>
        <DefaultNotificationMenu
          notifications={notifications}
          visibleNotifications={notifications.slice(0, 5)}
          remainingCount={10}
          showRemainingCount={true}
          isOpen={true}
        />
      </NotificationComponent>
    );
  },
  args: {
    badgeCount: '9+',
  },
};

export const BadgeOnly: Story = {
  render: (args) => {
    const notifications = [
      {
        id: '1',
        message: 'Nueva orden #1234 ha sido creada por Juan Perez',
        isRead: false,
        createdAt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutos atras
      },
    ];

    return (
      <NotificationComponent {...args}>
        <DefaultNotificationMenu
          notifications={notifications}
          visibleNotifications={notifications}
          remainingCount={0}
          showRemainingCount={false}
          isOpen={true}
        />
      </NotificationComponent>
    );
  },
  args: {
    badgeCount: 5,
  },
};

export const OnlyBell: Story = {
  args: {
    badgeCount: 3,
    // Sin notifications ni children - solo muestra el bell
  },
};

export const WithChildren: Story = {
  render: (args) => (
    <NotificationComponent {...args}>
      <div className="pd-p-4 pd-bg-white pd-shadow-lg pd-rounded-lg pd-border pd-min-w-80">
        <h3 className="pd-text-lg pd-font-semibold pd-mb-3 pd-text-gray-800">
          Custom Content
        </h3>
        <div className="pd-space-y-2">
          <div className="pd-p-3 pd-bg-blue-50 pd-rounded pd-border-l-4 pd-border-blue-400">
            <p className="pd-text-sm pd-text-blue-800">
              This is completely custom content using children!
            </p>
          </div>
          <div className="pd-p-3 pd-bg-green-50 pd-rounded pd-border-l-4 pd-border-green-400">
            <p className="pd-text-sm pd-text-green-800">
              You can put any React content here.
            </p>
          </div>
          <div className="pd-flex pd-gap-2 pd-mt-4">
            <button className="pd-px-3 pd-py-1 pd-bg-blue-500 pd-text-white pd-rounded pd-text-sm hover:pd-bg-blue-600">
              Action 1
            </button>
            <button className="pd-px-3 pd-py-1 pd-bg-gray-500 pd-text-white pd-rounded pd-text-sm hover:pd-bg-gray-600">
              Action 2
            </button>
          </div>
        </div>
      </div>
    </NotificationComponent>
  ),
  args: {
    badgeCount: 2,
  },
};

export const WithMenuAsChildren: Story = {
  render: (args) => {
    const notifications = [
      { id: '1', message: 'Welcome to our platform!', to: '#notification/1' },
      { id: '2', message: 'Your profile has been updated', to: '#notification/2' },
      { id: '3', message: 'New message from John Doe', to: '#notification/3' },
      { id: '4', message: 'System maintenance scheduled', to: '#notification/4' },
    ];

    return (
      <NotificationComponent {...args}>
        <StyledNotificationsMenuContainer isOpen={true}>
          <div className="pd-p-4 pd-bg-purple-50 pd-border-b">
            <Title as="h4" size="sm" weight="bold" className="pd-text-purple-800">
              Menu as Children
            </Title>
            <p className="pd-text-xs pd-text-purple-600 pd-mt-1">
              This menu is passed as children instead of using menuComponent prop
            </p>
          </div>
          <StyledNotificationsMenuMessages>
            {notifications.map((notification, index) => (
              <div
                key={notification.id}
                className="pd-p-3 pd-border-b pd-border-gray-100 hover:pd-bg-purple-50 pd-cursor-pointer pd-transition-colors"
              >
                <div className="pd-flex pd-items-start pd-gap-3">
                  <div className="pd-w-3 pd-h-3 pd-bg-purple-500 pd-rounded-full pd-mt-1 pd-flex-shrink-0"></div>
                  <div className="pd-flex-1">
                    <p className="pd-text-sm pd-font-medium pd-text-gray-900">
                      {notification.message}
                    </p>
                    <div className="pd-flex pd-justify-between pd-items-center pd-mt-1">
                      <span className="pd-text-xs pd-text-gray-500">
                        Notification #{index + 1}
                      </span>
                      <span className="pd-text-xs pd-text-purple-600 pd-font-medium">
                        2 min ago
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            <div className="pd-p-3 pd-text-center pd-bg-purple-50 pd-border-t">
              <button className="pd-text-sm pd-font-medium pd-text-purple-700 hover:pd-text-purple-900 pd-transition-colors">
                View all notifications
              </button>
            </div>
          </StyledNotificationsMenuMessages>
        </StyledNotificationsMenuContainer>
      </NotificationComponent>
    );
  },
  args: {
    badgeCount: 4,
  },
};

export const DefaultMenuAsChildren: Story = {
  render: (args) => {
    const notifications = [
      {
        id: '1',
        message: 'Nueva orden #1234 ha sido creada por Juan Perez',
        isRead: false,
        createdAt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutos atras
      },
      {
        id: '2',
        message: 'Alerta de inventario: Producto "iPhone 15" tiene stock bajo (3 unidades)',
        isRead: false,
        createdAt: new Date(Date.now() - 15 * 60 * 1000), // 15 minutos atras
      },
      {
        id: '3',
        message: 'Pago recibido para orden #1230 - $1,299.99',
        isRead: false,
        createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutos atras
      },
      {
        id: '4',
        message: 'Nuevo cliente registrado: Maria Garcia',
        isRead: false,
        createdAt: new Date(Date.now() - 45 * 60 * 1000), // 45 minutos atras
      },
      {
        id: '5',
        message: 'Codigo de descuento "SAVE20" ha sido activado',
        isRead: false,
        createdAt: new Date(Date.now() - 60 * 60 * 1000), // 1 hora atras
      },
    ];

    return (
      <NotificationComponent {...args}>
        <DefaultNotificationMenu
          notifications={notifications}
          visibleNotifications={notifications.slice(0, 2)}
          remainingCount={1}
          showRemainingCount={true}
          isOpen={true}
        />
      </NotificationComponent>
    );
  },
  args: {
    badgeCount: 3,
  },
};
