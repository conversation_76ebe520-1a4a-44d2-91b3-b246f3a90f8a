import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { But<PERSON> } from '../components/button/Button.Component';
import { DropdownGeneric } from '../components/dropdown/DropdownGeneric.Component';
import { IconImporter } from '../components/iconImporter/IconImporter.Component';
import { NotificationComponent } from '../components/notification/Notification.Component';
import { DefaultNotificationMenu } from '../components/notification/NotificationMenu.Component';

const meta = {
  title: 'Components/DropdownGeneric',
  component: DropdownGeneric,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Un componente de dropdown genérico que puede recibir cualquier contenido como prop, incluyendo componentes complejos como notificaciones.',
      },
    },
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#FFFFFF' },
        { name: 'dark', value: '#333333' },
        { name: 'gray', value: '#F5F5F5' },
      ],
    },
  },
  argTypes: {
    disabled: {
      control: 'boolean',
      description: 'Deshabilita el componente',
    },
    position: {
      control: 'select',
      options: ['top', 'bottom', 'left', 'right', 'top-left', 'top-right', 'bottom-left', 'bottom-right'],
      description: 'Posición donde se mostrará el dropdown',
    },
    trigger: {
      description: 'Elemento que actúa como disparador del dropdown',
      control: false,
    },
    content: {
      description: 'Contenido que se mostrará en el dropdown',
      control: false,
    },
  },
  decorators: [
    (Story) => (
      <div style={{ padding: '4rem', maxWidth: '600px' }}>
        <Story />
      </div>
    ),
  ],
} as Meta<typeof DropdownGeneric>;

type Story = StoryObj<typeof DropdownGeneric>;

// Datos de ejemplo para las notificaciones
const sampleNotifications = [
  {
    id: '1', message: 'Nueva actualización disponible', isRead: false, createdAt: new Date(),
  },
  {
    id: '2', message: 'Tu pedido ha sido enviado', isRead: false, createdAt: new Date(),
  },
  {
    id: '3', message: 'Recordatorio: Reunión en 30 minutos', isRead: true, createdAt: new Date(),
  },
  {
    id: '4', message: 'Mensaje muy largo para probar cómo se maneja el texto extenso en las notificaciones', isRead: false, createdAt: new Date(),
  },
];

export const WithNotifications: Story = {
  args: {
    position: 'bottom-right',
    disabled: false,
  },
  render: (args) => (
    <DropdownGeneric
      {...args}
      trigger={
        <Button size="small" className="pd-flex pd-items-center pd-gap-2">
          <IconImporter name="bell" />
          Notificaciones
          <span className="pd-bg-red-500 pd-text-white pd-text-xs pd-rounded-full pd-px-2 pd-py-1">
            {sampleNotifications.filter((n) => !n.isRead).length}
          </span>
        </Button>
      }
      content={
        <NotificationComponent
          notifications={sampleNotifications}
          badgeCount={sampleNotifications.filter((n) => !n.isRead).length}
        >
          <DefaultNotificationMenu
            isOpen={true}
            notifications={sampleNotifications}
            visibleNotifications={sampleNotifications}
            remainingCount={0}
            showRemainingCount={false}
          />
        </NotificationComponent>
      }
    />
  ),
};

export const WithManyNotifications: Story = {
  args: {
    position: 'bottom-right',
    disabled: false,
  },
  render: (args) => {
    const manyNotifications = Array.from({ length: 15 }, (_, i) => ({
      id: `${i + 1}`,
      message: `Notificación ${i + 1} - ${i % 3 === 0 ? 'Mensaje largo para probar el comportamiento con texto extenso' : 'Mensaje corto'}`,
      isRead: i % 5 === 0,
      createdAt: new Date(),
    }));

    return (
      <DropdownGeneric
        {...args}
        trigger={
          <Button size="small" className="pd-flex pd-items-center pd-gap-2">
            <IconImporter name="bell" />
            Muchas Notificaciones
            <span className="pd-bg-red-500 pd-text-white pd-text-xs pd-rounded-full pd-px-2 pd-py-1">
              9+
            </span>
          </Button>
        }
        content={
          <NotificationComponent badgeCount="9+">
            <div className="pd-p-4 pd-bg-white pd-shadow-lg pd-rounded-lg pd-border pd-min-w-80">
              <h3 className="pd-text-lg pd-font-semibold pd-mb-3 pd-text-gray-800">
                Many Notifications
              </h3>
              <div className="pd-space-y-2 pd-max-h-64 pd-overflow-y-auto">
                {manyNotifications.slice(0, 5).map((notification) => (
                  <div key={notification.id} className="pd-p-2 pd-border-b pd-border-gray-100">
                    <p className="pd-text-sm">{notification.message}</p>
                  </div>
                ))}
                <div className="pd-text-center pd-text-sm pd-text-gray-500 pd-mt-2">
                  {manyNotifications.length - 5} more notifications
                </div>
              </div>
            </div>
          </NotificationComponent>
        }
      />
    );
  },
};

export const NoNotifications: Story = {
  args: {
    position: 'bottom-right',
    disabled: false,
  },
  render: (args) => (
    <DropdownGeneric
      {...args}
      trigger={
        <Button size="small" className="pd-flex pd-items-center pd-gap-2">
          <IconImporter name="bell" />
          Sin Notificaciones
        </Button>
      }
      content={
        <NotificationComponent
          notifications={[]}
          badgeCount={0}
        >
          <DefaultNotificationMenu
            isOpen={true}
            notifications={[]}
            visibleNotifications={[]}
            remainingCount={0}
            showRemainingCount={false}
          />
        </NotificationComponent>
      }
    />
  ),
};

export const CustomContent: Story = {
  args: {
    position: 'bottom',
    disabled: false,
  },
  render: (args) => (
    <DropdownGeneric
      {...args}
      trigger={
        <Button size="small" className="pd-flex pd-items-center pd-gap-2">
          <IconImporter name="dotsThree" />
          Contenido Personalizado
        </Button>
      }
      content={
        <div className="pd-p-4 pd-min-w-64">
          <h3 className="pd-text-lg pd-font-semibold pd-mb-3">Menú Personalizado</h3>
          <div className="pd-space-y-2">
            <div className="pd-flex pd-items-center pd-gap-2 pd-p-2 pd-rounded pd-hover:bg-gray-100 pd-cursor-pointer">
              <IconImporter name="user" />
              <span>Perfil</span>
            </div>
            <div className="pd-flex pd-items-center pd-gap-2 pd-p-2 pd-rounded pd-hover:bg-gray-100 pd-cursor-pointer">
              <IconImporter name="gear" />
              <span>Configuración</span>
            </div>
            <div className="pd-flex pd-items-center pd-gap-2 pd-p-2 pd-rounded pd-hover:bg-gray-100 pd-cursor-pointer">
              <IconImporter name="arrowElbowRightDown" />
              <span>Cerrar Sesión</span>
            </div>
          </div>
        </div>
      }
    />
  ),
};

export const DifferentPositions: Story = {
  render: () => (
    <div className="pd-grid pd-grid-cols-2 pd-gap-8 pd-p-8">
      <DropdownGeneric
        position="top-left"
        trigger={
          <Button size="small">Top Left</Button>
        }
        content={
          <div className="pd-p-4 pd-min-w-48">
            <p>Contenido en la parte superior izquierda</p>
          </div>
        }
      />

      <DropdownGeneric
        position="top-right"
        trigger={
          <Button size="small">Top Right</Button>
        }
        content={
          <div className="pd-p-4 pd-min-w-48">
            <p>Contenido en la parte superior derecha</p>
          </div>
        }
      />

      <DropdownGeneric
        position="bottom-left"
        trigger={
          <Button size="small">Bottom Left</Button>
        }
        content={
          <div className="pd-p-4 pd-min-w-48">
            <p>Contenido en la parte inferior izquierda</p>
          </div>
        }
      />

      <DropdownGeneric
        position="bottom-right"
        trigger={
          <Button size="small">Bottom Right</Button>
        }
        content={
          <div className="pd-p-4 pd-min-w-48">
            <p>Contenido en la parte inferior derecha</p>
          </div>
        }
      />
    </div>
  ),
};

export const Disabled: Story = {
  args: {
    disabled: true,
  },
  render: (args) => (
    <DropdownGeneric
      {...args}
      trigger={
        <Button size="small" disabled>
          Dropdown Deshabilitado
        </Button>
      }
      content={
        <div className="pd-p-4">
          <p>Este contenido no debería mostrarse</p>
        </div>
      }
    />
  ),
};

export default meta;
