import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Sidebar } from '../components/sidebar/Sidebar.Component';

const sidebarGroups = [
  {
    groupLabel: 'Plataforma',
    items: [
      {
        id: 'store',
        label: 'Tienda',
        icon: 'storefront',
      },
      {
        id: 'claudia',
        label: '<PERSON>',
        icon: 'brain',
      },
      {
        id: 'inventory',
        label: 'Inventario',
        icon: 'stack',
      },
      {
        id: 'catalog',
        label: 'Catálogo',
        icon: 'package',
      },
      {
        id: 'clients',
        label: 'Clientes',
        icon: 'addressBook',
      },
      {
        id: 'providers',
        label: 'Proveedores',
        icon: 'factory',
        subItems: [
          {
            id: 'my-providers',
            label: 'Mis Proveedores',
          },
          {
            id: 'provider-catalog',
            label: 'Catálogo de Proveedores',
          },
        ],
      },
      {
        id: 'orders',
        label: 'Órdenes',
        icon: 'listChecks',
        subItems: [
          { id: 'purchase-orders', label: 'Órdenes de Compra', to: '/purchase-orders' },
          { id: 'sale-orders', label: '<PERSON><PERSON><PERSON> de Venta', to: '/sale-orders' },
        ],
      },
      {
        id: 'discounts',
        label: 'Descuentos',
        icon: 'tag',
        subItems: [
          { id: 'catalog-discounts', label: 'Descuentos de Catálogo', to: '#' },
          { id: 'store-discounts', label: 'Descuentos de Tienda', to: '#' },
        ],
      },
    ],
  },
  {
    groupLabel: 'Configuración',
    items: [
      {
        id: 'users',
        label: 'Usuarios',
        icon: 'users',
        to: '#',
      },
    ],
  },
];

const meta = {
  title: 'Components/Sidebar',
  component: Sidebar,
  parameters: {
    layout: 'centered',
    backgrounds: {
      default: 'dark',
    },
  },
  args: {
    sidebarGroups,
    path: '#',
    profileUserInfo: {
      // eslint-disable-next-line max-len
      userProfilePhoto: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
      userName: 'Juan Rodriguez',
      userRol: 'Admin',
      userEmail: '<EMAIL>',
    },
    headerSection: (
      <div className="pd-flex pd-items-center pd-gap-3 pd-px-2 pd-py-3 pd-mb-2 pd-rounded pd-bg-gray-700">
        <img
          src="https://img.freepik.com/free-psd/gradient-abstract-logo_23-2150689652.jpg?semt=ais_hybrid&w=740"
          alt="Suplifai Logo"
          className="pd-h-8 pd-w-8 pd-object-contain"
        />
        <div className="pd-flex pd-flex-col">
          <span className="pd-text-base pd-font-bold pd-text-white pd-leading-tight">Empresa</span>
        </div>
      </div>
    ),
    logoutButton: {
      // eslint-disable-next-line no-alert
      onClick: () => alert('Logout'),
      children: 'Logout',
    },
    userMenuItems: [
      {
        // eslint-disable-next-line no-alert
        id: '1', label: 'Ayuda', icon: 'question', onClick: () => alert('Ayuda clicked!'),
      },
      {
        // eslint-disable-next-line no-alert
        id: '2', label: 'Configuración', icon: 'gear', onClick: () => alert('Configuración clicked!'),
      },
    ],
  },
  argTypes: {
    items: {
      control: 'object',
    },
  },
} as Meta<typeof Sidebar>;

export default meta;

type Story = StoryObj<typeof Sidebar>;

export const DefaultSidebar: Story = {
  render: (args) => <Sidebar {...args} />,
};

// Story con sidebar mínimo (sin header ni user section)
export const MinimalSidebar: Story = {
  args: {
    sidebarGroups: [
      {
        groupLabel: 'Navegación',
        items: [
          {
            id: 'dashboard',
            label: 'Dashboard',
            icon: 'buildings',
            to: '/dashboard',
          },
          {
            id: 'settings',
            label: 'Configuración',
            icon: 'gear',
            to: '/settings',
          },
        ],
      },
    ],
    path: '/dashboard',
    headerSection: null,
    profileUserInfo: undefined,
    logoutButton: undefined,
    userMenuItems: undefined,
  },
  render: (args) => <Sidebar {...args} />,
};

// Story con elementos activos
export const WithActiveItems: Story = {
  args: {
    ...meta.args,
    path: '/purchase-orders', // Esto activará el item de "Órdenes de Compra"
  },
  render: (args) => <Sidebar {...args} />,
};

// Story sin información de usuario
export const WithoutUserInfo: Story = {
  args: {
    ...meta.args,
    profileUserInfo: undefined,
    logoutButton: undefined,
    userMenuItems: undefined,
  },
  render: (args) => <Sidebar {...args} />,
};

// Story con sidebar colapsado (simulando estado móvil)
export const CollapsedSidebar: Story = {
  args: {
    ...meta.args,
    className: 'pd-w-16', // Ancho reducido para simular colapso
    headerSection: (
      <div className="pd-flex pd-items-center pd-justify-center pd-px-2 pd-py-3 pd-mb-2">
        <img
          src="https://img.freepik.com/free-psd/gradient-abstract-logo_23-2150689652.jpg?semt=ais_hybrid&w=740"
          alt="Logo"
          className="pd-h-8 pd-w-8 pd-object-contain"
        />
      </div>
    ),
  },
  render: (args) => <Sidebar {...args} />,
};

// Story con muchos grupos y elementos
export const ExtendedSidebar: Story = {
  args: {
    ...meta.args,
    sidebarGroups: [
      {
        groupLabel: 'Plataforma',
        items: [
          {
            id: 'store',
            label: 'Tienda',
            icon: 'storefront',
          },
          {
            id: 'claudia',
            label: 'Claudia',
            icon: 'brain',
          },
          {
            id: 'inventory',
            label: 'Inventario',
            icon: 'stack',
          },
          {
            id: 'catalog',
            label: 'Catálogo',
            icon: 'package',
          },
          {
            id: 'clients',
            label: 'Clientes',
            icon: 'addressBook',
          },
          {
            id: 'providers',
            label: 'Proveedores',
            icon: 'factory',
            subItems: [
              {
                id: 'my-providers',
                label: 'Mis Proveedores',
                to: '/my-providers',
              },
              {
                id: 'provider-catalog',
                label: 'Catálogo de Proveedores',
                to: '/provider-catalog',
              },
            ],
          },
          {
            id: 'orders',
            label: 'Órdenes',
            icon: 'listChecks',
            subItems: [
              { id: 'purchase-orders', label: 'Órdenes de Compra', to: '/purchase-orders' },
              { id: 'sale-orders', label: 'Órdenes de Venta', to: '/sale-orders' },
            ],
          },
          {
            id: 'discounts',
            label: 'Descuentos',
            icon: 'tag',
            subItems: [
              { id: 'catalog-discounts', label: 'Descuentos de Catálogo', to: '/catalog-discounts' },
              { id: 'store-discounts', label: 'Descuentos de Tienda', to: '/store-discounts' },
            ],
          },
        ],
      },
      {
        groupLabel: 'Reportes',
        items: [
          {
            id: 'sales-reports',
            label: 'Reportes de Ventas',
            icon: 'wallet',
            to: '/reports/sales',
          },
          {
            id: 'inventory-reports',
            label: 'Reportes de Inventario',
            icon: 'stack',
            to: '/reports/inventory',
          },
          {
            id: 'financial-reports',
            label: 'Reportes Financieros',
            icon: 'card',
            subItems: [
              { id: 'profit-loss', label: 'Pérdidas y Ganancias', to: '/reports/profit-loss' },
              { id: 'balance-sheet', label: 'Balance General', to: '/reports/balance-sheet' },
              { id: 'cash-flow', label: 'Flujo de Caja', to: '/reports/cash-flow' },
            ],
          },
        ],
      },
      {
        groupLabel: 'Herramientas',
        items: [
          {
            id: 'import-export',
            label: 'Importar/Exportar',
            icon: 'arrowUUpLeft',
            // eslint-disable-next-line no-console
            onClick: () => console.log('Importar/Exportar clicked'),
          },
          {
            id: 'backup',
            label: 'Respaldo',
            icon: 'floppyDisk',
            // eslint-disable-next-line no-console
            onClick: () => console.log('Respaldo clicked'),
          },
          {
            id: 'integrations',
            label: 'Integraciones',
            icon: 'circlesThreePlus',
            subItems: [
              { id: 'api-keys', label: 'Claves API', to: '/integrations/api-keys' },
              { id: 'webhooks', label: 'Webhooks', to: '/integrations/webhooks' },
              { id: 'third-party', label: 'Terceros', to: '/integrations/third-party' },
            ],
          },
        ],
      },
      {
        groupLabel: 'Configuración',
        items: [
          {
            id: 'users',
            label: 'Usuarios',
            icon: 'users',
            to: '/users',
          },
        ],
      },
    ],
  },
  render: (args) => <Sidebar {...args} />,
};

// Story con diferentes tipos de usuario
export const DifferentUserRoles: Story = {
  args: {
    ...meta.args,
    profileUserInfo: {
      userProfilePhoto: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
      userName: 'María García',
      userRol: 'Vendedor',
      userEmail: '<EMAIL>',
    },
    sidebarGroups: [
      {
        groupLabel: 'Ventas',
        items: [
          {
            id: 'my-sales',
            label: 'Mis Ventas',
            icon: 'wallet',
            to: '/my-sales',
          },
          {
            id: 'customers',
            label: 'Clientes',
            icon: 'addressBook',
            to: '/customers',
          },
          {
            id: 'products',
            label: 'Productos',
            icon: 'package',
            to: '/products',
          },
        ],
      },
    ],
  },
  render: (args) => <Sidebar {...args} />,
};

// Story con elementos que solo tienen onClick (sin navegación)
export const WithClickableItems: Story = {
  args: {
    ...meta.args,
    sidebarGroups: [
      {
        groupLabel: 'Acciones',
        items: [
          {
            id: 'quick-sale',
            label: 'Venta Rápida',
            icon: 'shoppingCart',
            // eslint-disable-next-line no-console
            onClick: () => console.log('Iniciando venta rápida...'),
          },
          {
            id: 'scan-product',
            label: 'Escanear Producto',
            icon: 'search',
            // eslint-disable-next-line no-console
            onClick: () => console.log('Abriendo escáner...'),
          },
          {
            id: 'emergency-contact',
            label: 'Contacto de Emergencia',
            icon: 'bell',
            // eslint-disable-next-line no-console
            onClick: () => console.log('Llamando a soporte...'),
          },
        ],
      },
      {
        groupLabel: 'Plataforma',
        items: [
          {
            id: 'store',
            label: 'Tienda',
            icon: 'storefront',
          },
          {
            id: 'claudia',
            label: 'Claudia',
            icon: 'brain',
          },
          {
            id: 'inventory',
            label: 'Inventario',
            icon: 'stack',
          },
          {
            id: 'catalog',
            label: 'Catálogo',
            icon: 'package',
          },
          {
            id: 'clients',
            label: 'Clientes',
            icon: 'addressBook',
          },
          {
            id: 'providers',
            label: 'Proveedores',
            icon: 'factory',
            subItems: [
              {
                id: 'my-providers',
                label: 'Mis Proveedores',
                to: '/my-providers',
              },
              {
                id: 'provider-catalog',
                label: 'Catálogo de Proveedores',
                to: '/provider-catalog',
              },
            ],
          },
          {
            id: 'orders',
            label: 'Órdenes',
            icon: 'listChecks',
            subItems: [
              { id: 'purchase-orders', label: 'Órdenes de Compra', to: '/purchase-orders' },
              { id: 'sale-orders', label: 'Órdenes de Venta', to: '/sale-orders' },
            ],
          },
          {
            id: 'discounts',
            label: 'Descuentos',
            icon: 'tag',
            subItems: [
              { id: 'catalog-discounts', label: 'Descuentos de Catálogo', to: '/catalog-discounts' },
              { id: 'store-discounts', label: 'Descuentos de Tienda', to: '/store-discounts' },
            ],
          },
        ],
      },
      {
        groupLabel: 'Configuración',
        items: [
          {
            id: 'users',
            label: 'Usuarios',
            icon: 'users',
            to: '/users',
          },
        ],
      },
    ],
  },
  render: (args) => <Sidebar {...args} />,
};

// Story con header personalizado diferente
export const CustomHeaderSidebar: Story = {
  args: {
    ...meta.args,
    headerSection: (
      <div className={[
        'pd-flex pd-flex-col pd-items-center pd-gap-2 pd-px-2 pd-py-4 pd-mb-2 pd-rounded',
        'pd-bg-gradient-to-r pd-from-blue-500 pd-to-purple-600 pd-text-white',
      ].join(' ')}>
        <div className="pd-h-10 pd-w-10 pd-bg-white pd-rounded-full pd-flex pd-items-center pd-justify-center">
          <span className="pd-text-blue-600 pd-font-bold pd-text-lg">PD</span>
        </div>
        <div className="pd-text-center">
          <span className="pd-text-sm pd-font-bold pd-block">Pits Depot</span>
          <span className="pd-text-xs pd-opacity-90">Sistema de Gestión</span>
        </div>
      </div>
    ),
  },
  render: (args) => <Sidebar {...args} />,
};

// Story con menú de usuario extendido
export const ExtendedUserMenu: Story = {
  args: {
    ...meta.args,
    userMenuItems: [
      {
        id: 'profile',
        label: 'Mi Perfil',
        icon: 'user',
        // eslint-disable-next-line no-console
        onClick: () => console.log('Perfil clicked'),
      },
      {
        id: 'preferences',
        label: 'Preferencias',
        icon: 'gear',
        // eslint-disable-next-line no-console
        onClick: () => console.log('Preferencias clicked'),
      },
      {
        id: 'notifications',
        label: 'Notificaciones',
        icon: 'bell',
        // eslint-disable-next-line no-console
        onClick: () => console.log('Notificaciones clicked'),
      },
      {
        id: 'help',
        label: 'Ayuda y Soporte',
        icon: 'question',
        // eslint-disable-next-line no-console
        onClick: () => console.log('Ayuda clicked'),
      },
      {
        id: 'feedback',
        label: 'Enviar Feedback',
        icon: 'chat',
        // eslint-disable-next-line no-console
        onClick: () => console.log('Feedback clicked'),
      },
    ],
  },
  render: (args) => <Sidebar {...args} />,
};
