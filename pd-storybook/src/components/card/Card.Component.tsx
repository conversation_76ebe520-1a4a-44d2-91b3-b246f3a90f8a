import { RouteLinkProps } from '../routeLink/RouteLink.Component';
import { Title } from '../title/Title.Component';

export interface CardComponentProps {
  id: string;
  redirectTo: RouteLinkProps;
  title: string;
  imageUrl?: string;
  onImageError?: () => void;
  topPriceChild?: React.ReactNode;
  bottomPriceChild?: React.ReactNode;
  actionButton?: React.ReactNode;
  price: number;
  originalPrice?: number;
  appliedDiscountType?: 'percentage' | 'amount';
  appliedDiscount?: number;
  beforeTaxesLabel?: string;
  className?: string;
}

const BEFORE_TAXES_LABEL = 'Antes de impuestos';

export const CardComponent = ({
  imageUrl,
  onImageError,
  title,
  topPriceChild,
  bottomPriceChild,
  actionButton,
  price = 0,
  originalPrice,
  appliedDiscountType,
  appliedDiscount,
  beforeTaxesLabel = BEFORE_TAXES_LABEL,
  className = '',
}: CardComponentProps) => (
  <div
    className={`pd-flex-1 pd-box-border ${className}`}
    >
    <div className={'pd-bg-white pd-flex pd-justify-start pd-p-4 pd-rounded-2xl pd-transition-all pd-duration-300 pd-ease-in-out hover:pd-shadow-lg pd-h-full'} >
      <div className="pd-flex pd-flex-col pd-justify-start pd-items-center pd-gap-2">
        <div className="pd-w-full pd-h-auto pd-overflow-hidden">
          <img
            className="pd-w-full pd-h-full pd-object-cover pd-aspect-square pd-rounded-md"
            src={imageUrl}
            alt={title}
            loading="lazy"
            onError={onImageError}
            />
        </div>
        <div className='pd-flex pd-flex-col pd-w-full pd-gap-2'>
          <Title
            as='h3'
            size='xsm'
            className='pd-line-clamp-1'
            weight='regular'
            >
            {title}
          </Title>
          {topPriceChild}
          <div className='pd-flex pd-flex-col pd-justify-between'>
            <div className='pd-text-[8px] pd-text-dark-400 pd-leading-none'>{beforeTaxesLabel}</div>
            <div className='pd-flex pd-gap-2 pd-items-center pd-justify-between pd-w-full'>
              <div className='pd-flex pd-gap-2 flex-wrap'>
                <div className='pd-flex pd-flex-col pd-items-center pd-justify-center'>
                  <div className={`${originalPrice ? 'pd-text-dark-600' : 'pd-text-dark-900'} pd-text-2xl pd-font-semiBold`}>${price}</div>
                </div>
                <div className='pd-flex pd-flex-col pd-items-end'>
                  {originalPrice
                      && <>
                        <div className='pd-text-dark-500 pd-font-regular pd-text-xxsm pd-line-through'>${originalPrice}</div>
                        <div
                          className='pd-text-primary pd-font-regular pd-text-xxsm'
                        >
                          {appliedDiscountType === 'percentage' ? `-${appliedDiscount}%` : `$${appliedDiscount}`}
                        </div>
                      </>
                    }
                </div>
              </div>
              {actionButton}
            </div>
          </div>
          {bottomPriceChild}
        </div>
      </div>
    </div>
  </div>
);
