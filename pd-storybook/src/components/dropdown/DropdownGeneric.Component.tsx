import { ReactNode, useRef, useState } from 'react';

import OutsideClick from '../../utils/OutsideClick';

import { DropdownSimpeOptionsWrapper, DropdownWrapper } from './Dropdown.Style';

interface DropdownGenericProps {
  trigger: ReactNode;
  content: ReactNode;
  disabled?: boolean;
  onToggle?: (isOpen: boolean) => void;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  className?: string;
  isOpen?: boolean;
  onOpenChange?: (isOpen: boolean) => void;
}

export const DropdownGeneric: React.FC<DropdownGenericProps> = ({
  trigger,
  content,
  disabled = false,
  onToggle,
  position = 'bottom',
  className,
  isOpen: controlledIsOpen,
  onOpenChange,
}) => {
  const [internalIsOpen, setInternalIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Use controlled state if provided, otherwise use internal state
  const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : internalIsOpen;

  const toggleDropdown = (state: boolean) => {
    if (disabled) {
      return;
    }

    if (controlledIsOpen !== undefined && onOpenChange) {
      onOpenChange(state);
    } else {
      setInternalIsOpen(state);
    }

    if (onToggle) {
      onToggle(state);
    }
  };

  const handleTriggerClick = () => {
    if (!disabled) {
      toggleDropdown(!isOpen);
    }
  };

  return (
    <OutsideClick onOutsideClick={() => toggleDropdown(false)}>
      <DropdownWrapper className={className} ref={dropdownRef}>
        <div
          data-testid="dropdown-trigger"
          onClick={handleTriggerClick}
          style={{ cursor: disabled ? 'not-allowed' : 'pointer' }}
        >
          {trigger}
        </div>

        {isOpen && (
          <DropdownSimpeOptionsWrapper
            data-testid="dropdown-content"
            style={{
              position: 'absolute',
              zIndex: 1000,
              ...(position.includes('top') && { bottom: '100%' }),
              ...(position.includes('bottom') && { top: '100%' }),
              ...(position.includes('left') && { right: '100%' }),
              ...(position.includes('right') && { left: '100%' }),
              ...(position === 'top-left' && { bottom: '100%', right: '0' }),
              ...(position === 'top-right' && { bottom: '100%', left: '0' }),
              ...(position === 'bottom-left' && { top: '100%', right: '0' }),
              ...(position === 'bottom-right' && { top: '100%', left: '0' }),
            }}
          >
            {content}
          </DropdownSimpeOptionsWrapper>
        )}
      </DropdownWrapper>
    </OutsideClick>
  );
};
