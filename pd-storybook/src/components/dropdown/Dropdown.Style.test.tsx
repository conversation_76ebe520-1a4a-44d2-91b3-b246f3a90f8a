import { render } from '@testing-library/react';

import {
  Dropdown<PERSON>ontainer,
  DropdownHeader,
  DropdownLabel,
  DropdownOptionsContainer,
  DropdownOptionsWrapper,
  DropdownPosition,
  DropdownSimpeOptionsWrapper,
  DropdownWrapper,
  NoOptionsFound,
  Option,
  OptionImage,
  OptionItem,
  OptionListWrapper,
  SearchInput,
  SimpleOptionItem,
} from './Dropdown.Style';

describe('Dropdown Style Snapshots', () => {
  it('renders DropdownContainer correctly', () => {
    const { asFragment } = render(<DropdownContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders DropdownHeader correctly', () => {
    const { asFragment } = render(<DropdownHeader />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders DropdownLabel correctly', () => {
    const { asFragment } = render(<DropdownLabel />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders DropdownOptionsContainer correctly', () => {
    const { asFragment } = render(<DropdownOptionsContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders SearchInput correctly', () => {
    const { asFragment } = render(<SearchInput />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders Option correctly', () => {
    const { asFragment } = render(<Option />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders OptionImage correctly', () => {
    const { asFragment } = render(<OptionImage />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders NoOptionsFound correctly', () => {
    const { asFragment } = render(<NoOptionsFound />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders OptionListWrapper correctly', () => {
    const { asFragment } = render(<OptionListWrapper />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders DropdownWrapper correctly by default', () => {
    const { asFragment } = render(<DropdownWrapper />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders DropdownOptionsWrapper correctly by default (position bottom)', () => {
    const { asFragment } = render(<DropdownOptionsWrapper />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders OptionItem correctly by default', () => {
    const { asFragment } = render(<OptionItem />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders SimpleOptionItem correctly by default', () => {
    const { asFragment } = render(<SimpleOptionItem />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders DropdownSimpeOptionsWrapper correctly by default', () => {
    const { asFragment } = render(<DropdownSimpeOptionsWrapper />);
    expect(asFragment()).toMatchSnapshot();
  });

  describe('DropdownOptionsWrapper prop variations', () => {
    const positions: DropdownPosition[] = [
      'top',
      'top-left',
      'top-right',
      'left',
      'right',
      'bottom',
      'bottom-left',
      'bottom-right',
    ];

    positions.forEach((position) => {
      it(`renders DropdownOptionsWrapper correctly with position ${position}`, () => {
        const { asFragment } = render(<DropdownOptionsWrapper position={position} />);
        expect(asFragment()).toMatchSnapshot();
      });
    });

    it('renders DropdownOptionsWrapper correctly with invalid position (default case)', () => {
      const { asFragment } = render(<DropdownOptionsWrapper position={'invalid' as DropdownPosition} />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownOptionsWrapper correctly with minWidth prop', () => {
      const { asFragment } = render(<DropdownOptionsWrapper minWidth="200px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownOptionsWrapper correctly with maxWidth prop', () => {
      const { asFragment } = render(<DropdownOptionsWrapper maxWidth="400px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownOptionsWrapper correctly with minWidth and maxWidth props', () => {
      const { asFragment } = render(<DropdownOptionsWrapper minWidth="150px" maxWidth="350px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownOptionsWrapper correctly with top-left position and custom widths', () => {
      const { asFragment } = render(<DropdownOptionsWrapper position="top-left" minWidth="180px" maxWidth="320px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownOptionsWrapper correctly with top-right position and custom widths', () => {
      const { asFragment } = render(<DropdownOptionsWrapper position="top-right" minWidth="160px" maxWidth="300px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownOptionsWrapper correctly with bottom-left position and custom widths', () => {
      const { asFragment } = render(<DropdownOptionsWrapper position="bottom-left" minWidth="140px" maxWidth="280px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownOptionsWrapper correctly with bottom-right position and custom widths', () => {
      const { asFragment } = render(<DropdownOptionsWrapper position="bottom-right" minWidth="120px" maxWidth="260px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownOptionsWrapper correctly with left position and custom widths', () => {
      const { asFragment } = render(<DropdownOptionsWrapper position="left" minWidth="100px" maxWidth="240px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownOptionsWrapper correctly with right position and custom widths', () => {
      const { asFragment } = render(<DropdownOptionsWrapper position="right" minWidth="80px" maxWidth="220px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    // Test edge cases for width props
    it('renders DropdownOptionsWrapper correctly with undefined minWidth and maxWidth', () => {
      const { asFragment } = render(<DropdownOptionsWrapper position="top" minWidth={undefined} maxWidth={undefined} />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownOptionsWrapper correctly with empty string widths', () => {
      const { asFragment } = render(<DropdownOptionsWrapper position="left" minWidth="" maxWidth="" />);
      expect(asFragment()).toMatchSnapshot();
    });
  });

  describe('OptionItem prop variations', () => {
    it('renders OptionItem correctly with height prop', () => {
      const { asFragment } = render(<OptionItem height="60px" />);
      expect(asFragment()).toMatchSnapshot();
    });
  });

  describe('SimpleOptionItem prop variations', () => {
    it('renders SimpleOptionItem correctly with height prop', () => {
      const { asFragment } = render(<SimpleOptionItem height="30px" />);
      expect(asFragment()).toMatchSnapshot();
    });
  });

  describe('DropdownSimpeOptionsWrapper prop variations', () => {
    it('renders DropdownSimpeOptionsWrapper correctly with minWidth prop', () => {
      const { asFragment } = render(<DropdownSimpeOptionsWrapper minWidth="200px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownSimpeOptionsWrapper correctly with maxWidth prop', () => {
      const { asFragment } = render(<DropdownSimpeOptionsWrapper maxWidth="500px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownSimpeOptionsWrapper correctly with minWidth and maxWidth props', () => {
      const { asFragment } = render(<DropdownSimpeOptionsWrapper minWidth="180px" maxWidth="450px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownSimpeOptionsWrapper correctly with undefined props', () => {
      const { asFragment } = render(<DropdownSimpeOptionsWrapper minWidth={undefined} maxWidth={undefined} />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownSimpeOptionsWrapper correctly with empty string props', () => {
      const { asFragment } = render(<DropdownSimpeOptionsWrapper minWidth="" maxWidth="" />);
      expect(asFragment()).toMatchSnapshot();
    });
  });

  describe('Additional edge cases for complete branch coverage', () => {
    // Test OptionItem with undefined height to cover the || 'auto' branch
    it('renders OptionItem correctly with undefined height', () => {
      const { asFragment } = render(<OptionItem height={undefined} />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders OptionItem correctly with empty string height', () => {
      const { asFragment } = render(<OptionItem height="" />);
      expect(asFragment()).toMatchSnapshot();
    });

    // Test SimpleOptionItem with undefined height to cover the || 'auto' branch
    it('renders SimpleOptionItem correctly with undefined height', () => {
      const { asFragment } = render(<SimpleOptionItem height={undefined} />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders SimpleOptionItem correctly with empty string height', () => {
      const { asFragment } = render(<SimpleOptionItem height="" />);
      expect(asFragment()).toMatchSnapshot();
    });

    // Test Theme.colors.fadedGray fallback in OptionItem
    it('renders OptionItem correctly to test hover styles', () => {
      const { asFragment } = render(<OptionItem />);
      expect(asFragment()).toMatchSnapshot();
    });

    // Test Theme.colors.fadedGray in SimpleOptionItem
    it('renders SimpleOptionItem correctly to test hover styles', () => {
      const { asFragment } = render(<SimpleOptionItem />);
      expect(asFragment()).toMatchSnapshot();
    });

    // Test all DropdownOptionsWrapper positions with no width props to cover defaultSideWidth usage
    it('renders DropdownOptionsWrapper with left position and no width props', () => {
      const { asFragment } = render(<DropdownOptionsWrapper position="left" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownOptionsWrapper with right position and no width props', () => {
      const { asFragment } = render(<DropdownOptionsWrapper position="right" />);
      expect(asFragment()).toMatchSnapshot();
    });

    // Test the || operators in the styled components
    it('renders DropdownOptionsWrapper with all positions to test all || operators', () => {
      const positions: DropdownPosition[] = ['top', 'top-left', 'top-right', 'bottom', 'bottom-left', 'bottom-right', 'left', 'right'];
      positions.forEach((position) => {
        const { asFragment } = render(<DropdownOptionsWrapper position={position} />);
        expect(asFragment()).toMatchSnapshot();
      });
    });

    // Test to ensure all styled components are properly rendered and styled
    it('renders all styled components to ensure complete coverage', () => {
      const { asFragment: containerFragment } = render(<DropdownContainer />);
      const { asFragment: headerFragment } = render(<DropdownHeader />);
      const { asFragment: labelFragment } = render(<DropdownLabel />);
      const { asFragment: optionsContainerFragment } = render(<DropdownOptionsContainer />);
      const { asFragment: searchInputFragment } = render(<SearchInput />);
      const { asFragment: optionFragment } = render(<Option />);
      const { asFragment: optionImageFragment } = render(<OptionImage />);
      const { asFragment: noOptionsFragment } = render(<NoOptionsFound />);
      const { asFragment: optionListFragment } = render(<OptionListWrapper />);
      const { asFragment: wrapperFragment } = render(<DropdownWrapper />);

      expect(containerFragment()).toMatchSnapshot();
      expect(headerFragment()).toMatchSnapshot();
      expect(labelFragment()).toMatchSnapshot();
      expect(optionsContainerFragment()).toMatchSnapshot();
      expect(searchInputFragment()).toMatchSnapshot();
      expect(optionFragment()).toMatchSnapshot();
      expect(optionImageFragment()).toMatchSnapshot();
      expect(noOptionsFragment()).toMatchSnapshot();
      expect(optionListFragment()).toMatchSnapshot();
      expect(wrapperFragment()).toMatchSnapshot();
    });

    // Test to cover potential edge cases in styled-components
    it('renders styled components with various combinations to ensure all branches', () => {
      // Test OptionItem with various height values
      const heights = ['auto', '50px', '100px', undefined, ''];
      heights.forEach((height) => {
        const { asFragment } = render(<OptionItem height={height} />);
        expect(asFragment()).toMatchSnapshot();
      });

      // Test SimpleOptionItem with various height values
      heights.forEach((height) => {
        const { asFragment } = render(<SimpleOptionItem height={height} />);
        expect(asFragment()).toMatchSnapshot();
      });
    });
  });
});
