import {
  useState,
} from 'react';

import OutsideClick from '../../utils/OutsideClick';
import { Avatar } from '../avatar/Avatar.Component';
import { providersFormConstants } from '../providersForm/ProvidersForm.constant';
import { StyledOptionName } from '../providersForm/ProvidersForm.Style';

import {
  DropdownSimpeOptionsWrapper, DropdownWrapper, NoOptionsFound,
  OptionListWrapper,
  SimpleOptionItem,
} from './Dropdown.Style';
import { OptionsDropdownProps } from './DropdownWithSearch.Component';

  interface DropdownProps {
    options: OptionsDropdownProps[];
    children: React.ReactNode;
    setSelectedOption: (option: OptionsDropdownProps) => void;
    showAvatar?: boolean;
    disabled?: boolean;
    loadMoreOptions?: () => void;
    onToggle?: (isOpen: boolean) => void;
    position?: 'top' | 'bottom' | 'left' | 'right' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  }

export const DropdownSimple = ({
  options, children, setSelectedOption, showAvatar = false, disabled = false, loadMoreOptions, onToggle, position = 'bottom',
}: DropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleDropdown = (state: boolean) => {
    if (disabled) {
      return;
    }

    setIsOpen(state);
    if (onToggle) {
      onToggle(state);
    }
  };

  const handleOptionSelect = (option: OptionsDropdownProps) => {
    setSelectedOption(option);
    toggleDropdown(false);
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollTop + clientHeight >= scrollHeight && loadMoreOptions) {
      loadMoreOptions();
    }
  };

  const optionList = () => (options && options.length !== undefined && options.length > 0 ? (
    options.map((option, index) => {
      if (option?.renderer) {
        return (
          <div key={option.id} onClick={() => handleOptionSelect(option)}>
            {option?.renderer(option)}
          </div>
        );
      }

      return (
        <SimpleOptionItem
          data-testid='dropdown-item'
          key={index}
          height='40px'
          onClick={() => handleOptionSelect(option)}
        >
          {showAvatar && <Avatar
            name={option.name}
            size={36}
            />}

          <StyledOptionName>{option.name}</StyledOptionName>
        </SimpleOptionItem>

      );
    })
  ) : (
    <NoOptionsFound>{providersFormConstants.NO_OPTION_FOUND}</NoOptionsFound>
  ));

  return (
    <OutsideClick onOutsideClick={() => toggleDropdown(false)}>
      <DropdownWrapper >
        <div data-testid="dropdown-trigger" onClick={() => (disabled ? null : toggleDropdown(true))}>
          {children}
        </div>

        {isOpen && (
          <DropdownSimpeOptionsWrapper
            data-testid='dropdown'
            onScroll={handleScroll}
            style={{
              position: 'absolute',
              ...(position.includes('top') && { bottom: '100%' }),
              ...(position.includes('bottom') && { top: '100%' }),
              ...(position.includes('left') && { right: '100%' }),
              ...(position.includes('right') && { left: '100%' }),
            }}
          >
            <OptionListWrapper>
              {optionList()}
            </OptionListWrapper>
          </DropdownSimpeOptionsWrapper>
        )}
      </DropdownWrapper>
    </OutsideClick>
  );
};
