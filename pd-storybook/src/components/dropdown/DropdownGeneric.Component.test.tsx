import { fireEvent, render, screen } from '@testing-library/react';

import { NotificationComponent } from '../notification/Notification.Component';
import { DefaultNotificationMenu } from '../notification/NotificationMenu.Component';

import { DropdownGeneric } from './DropdownGeneric.Component';

describe('DropdownGeneric', () => {
  const mockTrigger = <button>Open Dropdown</button>;
  const mockContent = <div>Dropdown Content</div>;

  test('renders trigger element', () => {
    render(
      <DropdownGeneric
        trigger={mockTrigger}
        content={mockContent}
      />,
    );

    expect(screen.getByText('Open Dropdown')).toBeInTheDocument();
  });

  test('shows content when trigger is clicked', () => {
    render(
      <DropdownGeneric
        trigger={mockTrigger}
        content={mockContent}
      />,
    );

    const trigger = screen.getByTestId('dropdown-trigger');
    fireEvent.click(trigger);

    expect(screen.getByText('Dropdown Content')).toBeInTheDocument();
  });

  test('hides content when trigger is clicked again', () => {
    render(
      <DropdownGeneric
        trigger={mockTrigger}
        content={mockContent}
      />,
    );

    const trigger = screen.getByTestId('dropdown-trigger');

    // Open dropdown
    fireEvent.click(trigger);
    expect(screen.getByText('Dropdown Content')).toBeInTheDocument();

    // Close dropdown
    fireEvent.click(trigger);
    expect(screen.queryByText('Dropdown Content')).not.toBeInTheDocument();
  });

  test('does not show content when disabled', () => {
    render(
      <DropdownGeneric
        trigger={mockTrigger}
        content={mockContent}
        disabled={true}
      />,
    );

    const trigger = screen.getByTestId('dropdown-trigger');
    fireEvent.click(trigger);

    expect(screen.queryByText('Dropdown Content')).not.toBeInTheDocument();
  });

  test('works with NotificationComponent as content', () => {
    const notifications = [
      {
        id: '1', message: 'Test notification', isRead: false, createdAt: new Date(),
      },
    ];

    render(
      <DropdownGeneric
        trigger={mockTrigger}
        content={
          <NotificationComponent
            notifications={notifications}
            badgeCount={1}
          >
            <DefaultNotificationMenu
              isOpen={true}
              notifications={notifications}
              visibleNotifications={notifications}
              remainingCount={0}
              showRemainingCount={false}
            />
          </NotificationComponent>
        }
      />,
    );

    const trigger = screen.getByTestId('dropdown-trigger');
    fireEvent.click(trigger);

    // Click on the notification button inside the NotificationComponent
    const notificationButton = screen.getByTestId('notificationButton');
    fireEvent.click(notificationButton);

    expect(screen.getByText('Test notification')).toBeInTheDocument();
  });

  test('calls onToggle when dropdown state changes', () => {
    const mockOnToggle = jest.fn();

    render(
      <DropdownGeneric
        trigger={mockTrigger}
        content={mockContent}
        onToggle={mockOnToggle}
      />,
    );

    const trigger = screen.getByTestId('dropdown-trigger');
    fireEvent.click(trigger);

    expect(mockOnToggle).toHaveBeenCalledWith(true);
  });

  test('supports controlled state', () => {
    const mockOnOpenChange = jest.fn();

    render(
      <DropdownGeneric
        trigger={mockTrigger}
        content={mockContent}
        isOpen={true}
        onOpenChange={mockOnOpenChange}
      />,
    );

    // Content should be visible due to controlled state
    expect(screen.getByText('Dropdown Content')).toBeInTheDocument();

    const trigger = screen.getByTestId('dropdown-trigger');
    fireEvent.click(trigger);

    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });
});
