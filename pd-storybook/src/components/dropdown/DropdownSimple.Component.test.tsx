import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';

import { DropdownSimple } from './DropdownSimple.Component';
import { OptionsDropdownProps } from './DropdownWithSearch.Component';

jest.mock('../../utils/OutsideClick', () => ({
  __esModule: true,
  default: ({ children, onOutsideClick }: { children: React.ReactNode, onOutsideClick: () => void }) => (
    <div data-testid="outside-click" onClick={onOutsideClick}>
      {children}
    </div>
  ),
}));

jest.mock('../avatar/Avatar.Component', () => ({
  Avatar: ({ name, size }: { name: string, size: number }) => (
    <div data-testid="avatar-component" data-name={name} data-size={size}>
      Avatar for {name}
    </div>
  ),
}));

describe('DropdownSimple', () => {
  const mockOptions: OptionsDropdownProps[] = [
    { id: '1', name: 'Option 1' },
    { id: '2', name: 'Option 2' },
    { id: '3', name: 'Option 3' },
  ];

  const mockSetSelectedOption = jest.fn();
  const mockOnToggle = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders TooltipContainer correctly', () => {
    const { asFragment } = render(<DropdownSimple
      options={mockOptions}
      setSelectedOption={mockSetSelectedOption}
    >
      <button>Open Dropdown</button>
    </DropdownSimple>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders the dropdown with children', () => {
    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    expect(screen.getByText('Open Dropdown')).toBeInTheDocument();
    expect(screen.queryByTestId('dropdown')).not.toBeInTheDocument();
  });

  it('does not open the dropdown when disabled', () => {
    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        disabled={true}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByText('Open Dropdown'));
    expect(screen.queryByTestId('dropdown')).not.toBeInTheDocument();
  });

  it('calls onToggle when dropdown is opened', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        onToggle={mockOnToggle}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByText('Open Dropdown'));
    expect(mockOnToggle).toHaveBeenCalledWith(true);

    const dropdown = screen.getByTestId('dropdown');

    expect(dropdown).toBeVisible();
  });

  test('should call setSelectedOption when an option is selected', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [{ id: '2', name: 'Option 2' }, jest.fn()]);

    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        onToggle={mockOnToggle}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByTestId('dropdown-trigger'));

    expect(screen.getByTestId('dropdown')).toBeInTheDocument();
    expect(mockOnToggle).toHaveBeenCalledWith(true);

    fireEvent.click(screen.getAllByTestId('dropdown-item')[1]); // Selecting Option 2

    expect(mockSetSelectedOption).toHaveBeenCalledWith(mockOptions[1]);
  });

  test('should handle option selection with custom renderer', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);
    const mockRenderer = jest.fn((option: OptionsDropdownProps) => <div data-testid="custom-rendered-option">Custom {option.name}</div>);
    const optionsWithRenderer = [
      {
        id: '1',
        name: 'Custom Option',
        renderer: mockRenderer,
      },
    ];

    render(
      <DropdownSimple
        options={optionsWithRenderer}
        setSelectedOption={mockSetSelectedOption}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByTestId('dropdown-trigger'));

    expect(screen.getByTestId('custom-rendered-option')).toBeInTheDocument();
    expect(mockRenderer).toHaveBeenCalledWith(optionsWithRenderer[0]);

    fireEvent.click(screen.getByTestId('custom-rendered-option'));

    expect(mockSetSelectedOption).toHaveBeenCalledWith(optionsWithRenderer[0]);
  });

  it('should not call loadMoreOptions when not scrolled to the bottom', () => {
    const mockLoadMoreOptions = jest.fn();
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        loadMoreOptions={mockLoadMoreOptions}
      >
        <span>Dropdown Trigger</span>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByText('Dropdown Trigger'));

    const dropdown = screen.getByTestId('dropdown');

    Object.defineProperty(dropdown, 'scrollTop', { value: 10 });
    Object.defineProperty(dropdown, 'scrollHeight', { value: 150 });
    Object.defineProperty(dropdown, 'clientHeight', { value: 50 });

    fireEvent.scroll(dropdown);

    expect(mockLoadMoreOptions).not.toHaveBeenCalled();
  });

  it('should not throw error when loadMoreOptions is not provided', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
      >
        <span>Dropdown Trigger</span>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByText('Dropdown Trigger'));

    const dropdown = screen.getByTestId('dropdown');

    Object.defineProperty(dropdown, 'scrollTop', { value: 100 });
    Object.defineProperty(dropdown, 'scrollHeight', { value: 150 });
    Object.defineProperty(dropdown, 'clientHeight', { value: 50 });

    expect(() => {
      fireEvent.scroll(dropdown);
    }).not.toThrow();
  });

  it('should call loadMoreOptions when scrolled to the bottom', () => {
    const mockLoadMoreOptions = jest.fn();
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        loadMoreOptions={mockLoadMoreOptions}
      >
        <span>Dropdown Trigger</span>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByText('Dropdown Trigger'));

    const dropdown = screen.getByTestId('dropdown');

    Object.defineProperty(dropdown, 'scrollTop', { value: 100 });
    Object.defineProperty(dropdown, 'scrollHeight', { value: 150 });
    Object.defineProperty(dropdown, 'clientHeight', { value: 50 });

    fireEvent.scroll(dropdown);

    expect(mockLoadMoreOptions).toHaveBeenCalled();
  });

  it('should render "No options found" when options array is empty', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    render(
      <DropdownSimple
        options={[]}
        setSelectedOption={mockSetSelectedOption}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByTestId('dropdown-trigger'));

    expect(screen.getByTestId('dropdown')).toBeInTheDocument();
    expect(screen.getByText('No se encontraron opciones')).toBeInTheDocument();
  });

  it('should render "No options found" when options.length is undefined', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    // Create an array-like object without length property
    type ArrayLikeWithoutLength = {
      [index: number]: OptionsDropdownProps;
    };

    const optionsWithUndefinedLength: ArrayLikeWithoutLength = {
      0: { id: '1', name: 'Option 1' },
      1: { id: '2', name: 'Option 2' },
      // Intentionally no length property
    };

    render(
      <DropdownSimple
        options={optionsWithUndefinedLength as OptionsDropdownProps[]}
        setSelectedOption={mockSetSelectedOption}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByTestId('dropdown-trigger'));

    expect(screen.getByTestId('dropdown')).toBeInTheDocument();
    expect(screen.getByText('No se encontraron opciones')).toBeInTheDocument();
  });

  it('should render options with avatars when showAvatar is true', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        showAvatar={true}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByTestId('dropdown-trigger'));

    expect(screen.getByTestId('dropdown')).toBeInTheDocument();

    // Check that avatars are rendered for each option
    const avatars = screen.getAllByTestId('avatar-component');
    expect(avatars).toHaveLength(mockOptions.length);
  });

  it('should render options without avatars when showAvatar is false (default)', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByTestId('dropdown-trigger'));

    expect(screen.getByTestId('dropdown')).toBeInTheDocument();

    // Check that no avatars are rendered
    expect(screen.queryByTestId('avatar-component')).not.toBeInTheDocument();
  });

  it('should handle toggleDropdown when disabled is false (default)', () => {
    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    // Should be able to open dropdown when not disabled
    fireEvent.click(screen.getByTestId('dropdown-trigger'));

    // Check that dropdown opens (we can't see it because isOpen starts as false,
    // but we can verify the click handler works by checking no error is thrown)
    expect(() => {
      fireEvent.click(screen.getByTestId('dropdown-trigger'));
    }).not.toThrow();
  });

  it('should call onToggle when provided and dropdown state changes', () => {
    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        onToggle={mockOnToggle}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    // Open dropdown
    fireEvent.click(screen.getByTestId('dropdown-trigger'));
    expect(mockOnToggle).toHaveBeenCalledWith(true);

    // Close dropdown by clicking outside
    fireEvent.click(screen.getByTestId('outside-click'));
    expect(mockOnToggle).toHaveBeenCalledWith(false);
  });

  it('should not call onToggle when not provided', () => {
    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    // Should not throw error when onToggle is not provided
    expect(() => {
      fireEvent.click(screen.getByTestId('dropdown-trigger'));
    }).not.toThrow();
  });

  it('should close dropdown when clicking outside', () => {
    const mockSetIsOpen = jest.fn();
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, mockSetIsOpen]);

    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        onToggle={mockOnToggle}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    // Dropdown should be open initially
    expect(screen.getByTestId('dropdown')).toBeInTheDocument();

    // Click outside to close
    fireEvent.click(screen.getByTestId('outside-click'));
    expect(mockSetIsOpen).toHaveBeenCalledWith(false);
    expect(mockOnToggle).toHaveBeenCalledWith(false);
  });

  it('should render options without custom renderer when renderer is falsy', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    const optionsWithFalsyRenderer: OptionsDropdownProps[] = [
      {
        id: '1',
        name: 'Option with falsy renderer',
        renderer: null as unknown as (value: OptionsDropdownProps) => React.ReactNode,
      },
      {
        id: '2',
        name: 'Option with undefined renderer',
        renderer: undefined,
      },
    ];

    render(
      <DropdownSimple
        options={optionsWithFalsyRenderer}
        setSelectedOption={mockSetSelectedOption}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByTestId('dropdown-trigger'));

    expect(screen.getByTestId('dropdown')).toBeInTheDocument();

    // Should render normal options since renderer is falsy
    const dropdownItems = screen.getAllByTestId('dropdown-item');
    expect(dropdownItems).toHaveLength(2);
    expect(screen.getByText('Option with falsy renderer')).toBeInTheDocument();
    expect(screen.getByText('Option with undefined renderer')).toBeInTheDocument();
  });

  it('should execute custom renderer function and render its output', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    const mockRenderer = jest.fn((option: OptionsDropdownProps) => (
      <div data-testid="custom-renderer-output">
        Rendered: {option.name}
      </div>
    ));

    const optionsWithTruthyRenderer = [
      {
        id: '1',
        name: 'Test Option',
        renderer: mockRenderer,
      },
    ];

    render(
      <DropdownSimple
        options={optionsWithTruthyRenderer}
        setSelectedOption={mockSetSelectedOption}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    // Dropdown should render with custom renderer
    expect(screen.getByTestId('custom-renderer-output')).toBeInTheDocument();
    expect(screen.getByText('Rendered: Test Option')).toBeInTheDocument();
    expect(mockRenderer).toHaveBeenCalledWith(optionsWithTruthyRenderer[0]);

    // Click on the custom rendered option
    fireEvent.click(screen.getByTestId('custom-renderer-output'));
    expect(mockSetSelectedOption).toHaveBeenCalledWith(optionsWithTruthyRenderer[0]);
  });

  it('should render custom renderer with proper key and click handler', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    // Create an option with a renderer that returns a specific element
    const customRenderer = (option: OptionsDropdownProps) => (
      <span data-testid={`custom-option-${option.id}`}>
        Custom: {option.name}
      </span>
    );

    const optionWithRenderer = {
      id: 'test-id-123',
      name: 'Test Option with Renderer',
      renderer: customRenderer,
    };

    render(
      <DropdownSimple
        options={[optionWithRenderer]}
        setSelectedOption={mockSetSelectedOption}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    // The custom renderer should be executed and the element should be present
    const customElement = screen.getByTestId('custom-option-test-id-123');
    expect(customElement).toBeInTheDocument();
    expect(customElement).toHaveTextContent('Custom: Test Option with Renderer');

    // The element should be clickable and call handleOptionSelect
    fireEvent.click(customElement);
    expect(mockSetSelectedOption).toHaveBeenCalledWith(optionWithRenderer);
  });

  it('should cover the renderer branch with a simple function', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    // Simple renderer function
    const simpleRenderer = () => <div data-testid="simple-renderer">Simple</div>;

    const optionWithSimpleRenderer = {
      id: 'simple-id',
      name: 'Simple Option',
      renderer: simpleRenderer,
    };

    render(
      <DropdownSimple
        options={[optionWithSimpleRenderer]}
        setSelectedOption={mockSetSelectedOption}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    // Should render the simple renderer
    expect(screen.getByTestId('simple-renderer')).toBeInTheDocument();

    // Should be clickable
    fireEvent.click(screen.getByTestId('simple-renderer'));
    expect(mockSetSelectedOption).toHaveBeenCalledWith(optionWithSimpleRenderer);
  });
});
