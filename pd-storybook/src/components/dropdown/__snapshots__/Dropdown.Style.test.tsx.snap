// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders DropdownOptionsWrapper with all positions to test all || operators 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  bottom: 100%;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin-bottom: 6px;
  margin-top: 0;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders DropdownOptionsWrapper with all positions to test all || operators 2`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  bottom: 100%;
  left: 0;
  right: auto;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin-bottom: 6px;
  margin-top: 0;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders DropdownOptionsWrapper with all positions to test all || operators 3`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  bottom: 100%;
  right: 0;
  left: auto;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin-bottom: 6px;
  margin-top: 0;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders DropdownOptionsWrapper with all positions to test all || operators 4`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 100%;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin-top: 6px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders DropdownOptionsWrapper with all positions to test all || operators 5`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 100%;
  left: 0;
  right: auto;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin-top: 6px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders DropdownOptionsWrapper with all positions to test all || operators 6`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 100%;
  right: 0;
  left: auto;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin-top: 6px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders DropdownOptionsWrapper with all positions to test all || operators 7`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 0;
  right: 100%;
  width: 250px;
  min-width: auto;
  max-width: auto;
  margin-right: 6px;
  margin-top: 0;
  left: auto;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders DropdownOptionsWrapper with all positions to test all || operators 8`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 0;
  left: 100%;
  width: 250px;
  min-width: auto;
  max-width: auto;
  margin-left: 6px;
  margin-top: 0;
  right: auto;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders DropdownOptionsWrapper with left position and no width props 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 0;
  right: 100%;
  width: 250px;
  min-width: auto;
  max-width: auto;
  margin-right: 6px;
  margin-top: 0;
  left: auto;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders DropdownOptionsWrapper with right position and no width props 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 0;
  left: 100%;
  width: 250px;
  min-width: auto;
  max-width: auto;
  margin-left: 6px;
  margin-top: 0;
  right: auto;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders OptionItem correctly to test hover styles 1`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 10px 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders OptionItem correctly with empty string height 1`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 10px 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
    height=""
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders OptionItem correctly with undefined height 1`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 10px 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders SimpleOptionItem correctly to test hover styles 1`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 14px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 6px;
  fons-size: 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders SimpleOptionItem correctly with empty string height 1`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 14px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 6px;
  fons-size: 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
    height=""
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders SimpleOptionItem correctly with undefined height 1`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 14px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 6px;
  fons-size: 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders all styled components to ensure complete coverage 1`] = `
<DocumentFragment>
  .emotion-0 {
  border: 1px solid #D3D5DA;
  padding: 24px 16px;
  border-radius: 12px;
  min-width: 323px;
  cursor: pointer;
  position: relative;
  background: white;
  z-index: 50;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders all styled components to ensure complete coverage 2`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  border-bottom: 1px solid #D3D5DA;
  padding: 12px 16px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders all styled components to ensure complete coverage 3`] = `
<DocumentFragment>
  .emotion-0 {
  color: #6B6E75;
}

<span
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders all styled components to ensure complete coverage 4`] = `
<DocumentFragment>
  .emotion-0 {
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
  border-bottom: 1px solid #D3D5DA;
  border-radius: 8px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders all styled components to ensure complete coverage 5`] = `
<DocumentFragment>
  .emotion-0 {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #D3D5DA;
  border-radius: 4px;
  outline: none;
  margin-bottom: 8px;
  box-sizing: border-box;
  font-size: 14px;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders all styled components to ensure complete coverage 6`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 8px 16px;
  cursor: pointer;
  border-bottom: 1px solid #D3D5DA;
  height: 48px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 14px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders all styled components to ensure complete coverage 7`] = `
<DocumentFragment>
  .emotion-0 {
  width: 36px;
  height: 36px;
}

<img
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders all styled components to ensure complete coverage 8`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 12px 16px;
  color: #A3A5AB;
  text-align: center;
  font-size: 14px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders all styled components to ensure complete coverage 9`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders all styled components to ensure complete coverage 10`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  position: relative;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders styled components with various combinations to ensure all branches 1`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 10px 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
    height="auto"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders styled components with various combinations to ensure all branches 2`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  height: 50px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 10px 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
    height="50px"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders styled components with various combinations to ensure all branches 3`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  height: 100px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 10px 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
    height="100px"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders styled components with various combinations to ensure all branches 4`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 10px 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders styled components with various combinations to ensure all branches 5`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 10px 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
    height=""
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders styled components with various combinations to ensure all branches 6`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 14px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 6px;
  fons-size: 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
    height="auto"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders styled components with various combinations to ensure all branches 7`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 14px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 6px;
  fons-size: 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
    height="50px"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders styled components with various combinations to ensure all branches 8`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 14px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 6px;
  fons-size: 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
    height="100px"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders styled components with various combinations to ensure all branches 9`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 14px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 6px;
  fons-size: 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots Additional edge cases for complete branch coverage renders styled components with various combinations to ensure all branches 10`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 14px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 6px;
  fons-size: 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
    height=""
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with bottom-left position and custom widths 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 100%;
  left: 0;
  right: auto;
  width: 100%;
  min-width: 140px;
  max-width: 280px;
  margin-top: 6px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with bottom-right position and custom widths 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 100%;
  right: 0;
  left: auto;
  width: 100%;
  min-width: 120px;
  max-width: 260px;
  margin-top: 6px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with empty string widths 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 0;
  right: 100%;
  width: 250px;
  min-width: auto;
  max-width: auto;
  margin-right: 6px;
  margin-top: 0;
  left: auto;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with invalid position (default case) 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 100%;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin-top: 6px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with left position and custom widths 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 0;
  right: 100%;
  width: 100px;
  min-width: 100px;
  max-width: 240px;
  margin-right: 6px;
  margin-top: 0;
  left: auto;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with maxWidth prop 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 100%;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100%;
  max-width: 400px;
  margin-top: 6px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with minWidth and maxWidth props 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 100%;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 150px;
  max-width: 350px;
  margin-top: 6px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with minWidth prop 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 100%;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 200px;
  max-width: 100%;
  margin-top: 6px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with position bottom 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 100%;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin-top: 6px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with position bottom-left 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 100%;
  left: 0;
  right: auto;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin-top: 6px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with position bottom-right 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 100%;
  right: 0;
  left: auto;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin-top: 6px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with position left 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 0;
  right: 100%;
  width: 250px;
  min-width: auto;
  max-width: auto;
  margin-right: 6px;
  margin-top: 0;
  left: auto;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with position right 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 0;
  left: 100%;
  width: 250px;
  min-width: auto;
  max-width: auto;
  margin-left: 6px;
  margin-top: 0;
  right: auto;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with position top 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  bottom: 100%;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin-bottom: 6px;
  margin-top: 0;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with position top-left 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  bottom: 100%;
  left: 0;
  right: auto;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin-bottom: 6px;
  margin-top: 0;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with position top-right 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  bottom: 100%;
  right: 0;
  left: auto;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin-bottom: 6px;
  margin-top: 0;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with right position and custom widths 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 0;
  left: 100%;
  width: 80px;
  min-width: 80px;
  max-width: 220px;
  margin-left: 6px;
  margin-top: 0;
  right: auto;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with top-left position and custom widths 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  bottom: 100%;
  left: 0;
  right: auto;
  width: 100%;
  min-width: 180px;
  max-width: 320px;
  margin-bottom: 6px;
  margin-top: 0;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with top-right position and custom widths 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  bottom: 100%;
  right: 0;
  left: auto;
  width: 100%;
  min-width: 160px;
  max-width: 300px;
  margin-bottom: 6px;
  margin-top: 0;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownOptionsWrapper prop variations renders DropdownOptionsWrapper correctly with undefined minWidth and maxWidth 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  bottom: 100%;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin-bottom: 6px;
  margin-top: 0;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownSimpeOptionsWrapper prop variations renders DropdownSimpeOptionsWrapper correctly with empty string props 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
  border-bottom: 1px solid #D3D5DA;
  border-radius: 8px;
  box-shadow: 0px 6px 8px #D3D5DA;
  position: absolute;
  padding: 8px;
  background: white;
  width: 100%;
  max-width: 100%;
  position: absolute;
  z-index: 50;
  left: 0;
  right: 0;
  box-sizing: border-box;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownSimpeOptionsWrapper prop variations renders DropdownSimpeOptionsWrapper correctly with maxWidth prop 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
  border-bottom: 1px solid #D3D5DA;
  border-radius: 8px;
  box-shadow: 0px 6px 8px #D3D5DA;
  position: absolute;
  padding: 8px;
  background: white;
  width: 100%;
  max-width: 500px;
  position: absolute;
  z-index: 50;
  left: 0;
  right: 0;
  box-sizing: border-box;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownSimpeOptionsWrapper prop variations renders DropdownSimpeOptionsWrapper correctly with minWidth and maxWidth props 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
  border-bottom: 1px solid #D3D5DA;
  border-radius: 8px;
  box-shadow: 0px 6px 8px #D3D5DA;
  position: absolute;
  padding: 8px;
  background: white;
  width: 100%;
  max-width: 450px;
  position: absolute;
  z-index: 50;
  left: 0;
  right: 0;
  box-sizing: border-box;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownSimpeOptionsWrapper prop variations renders DropdownSimpeOptionsWrapper correctly with minWidth prop 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
  border-bottom: 1px solid #D3D5DA;
  border-radius: 8px;
  box-shadow: 0px 6px 8px #D3D5DA;
  position: absolute;
  padding: 8px;
  background: white;
  width: 100%;
  max-width: 100%;
  position: absolute;
  z-index: 50;
  left: 0;
  right: 0;
  box-sizing: border-box;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots DropdownSimpeOptionsWrapper prop variations renders DropdownSimpeOptionsWrapper correctly with undefined props 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
  border-bottom: 1px solid #D3D5DA;
  border-radius: 8px;
  box-shadow: 0px 6px 8px #D3D5DA;
  position: absolute;
  padding: 8px;
  background: white;
  width: 100%;
  max-width: 100%;
  position: absolute;
  z-index: 50;
  left: 0;
  right: 0;
  box-sizing: border-box;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots OptionItem prop variations renders OptionItem correctly with height prop 1`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  height: 60px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 10px 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
    height="60px"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots SimpleOptionItem prop variations renders SimpleOptionItem correctly with height prop 1`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 14px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 6px;
  fons-size: 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
    height="30px"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots renders DropdownContainer correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  border: 1px solid #D3D5DA;
  padding: 24px 16px;
  border-radius: 12px;
  min-width: 323px;
  cursor: pointer;
  position: relative;
  background: white;
  z-index: 50;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots renders DropdownHeader correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  border-bottom: 1px solid #D3D5DA;
  padding: 12px 16px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots renders DropdownLabel correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #6B6E75;
}

<span
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots renders DropdownOptionsContainer correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
  border-bottom: 1px solid #D3D5DA;
  border-radius: 8px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots renders DropdownOptionsWrapper correctly by default (position bottom) 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #F6F7FA;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;
  top: 100%;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  margin-top: 6px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots renders DropdownSimpeOptionsWrapper correctly by default 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
  border-bottom: 1px solid #D3D5DA;
  border-radius: 8px;
  box-shadow: 0px 6px 8px #D3D5DA;
  position: absolute;
  padding: 8px;
  background: white;
  width: 100%;
  max-width: 100%;
  position: absolute;
  z-index: 50;
  left: 0;
  right: 0;
  box-sizing: border-box;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots renders DropdownWrapper correctly by default 1`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  position: relative;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots renders NoOptionsFound correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 12px 16px;
  color: #A3A5AB;
  text-align: center;
  font-size: 14px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots renders Option correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 8px 16px;
  cursor: pointer;
  border-bottom: 1px solid #D3D5DA;
  height: 48px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 14px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots renders OptionImage correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: 36px;
  height: 36px;
}

<img
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots renders OptionItem correctly by default 1`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 12px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 10px 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots renders OptionListWrapper correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots renders SearchInput correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #D3D5DA;
  border-radius: 4px;
  outline: none;
  margin-bottom: 8px;
  box-sizing: border-box;
  font-size: 14px;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dropdown Style Snapshots renders SimpleOptionItem correctly by default 1`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 14px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 6px;
  fons-size: 12px;
}

.emotion-0:hover {
  background: #F4F6F9;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;
