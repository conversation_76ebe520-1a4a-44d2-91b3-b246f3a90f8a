// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StyledRouteLink Snapshots renders correctly as a anchor element 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  text-decoration-line: none;
  font-size: base;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    Default Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with custom className 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  text-decoration-line: none;
  font-size: base;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="custom-class emotion-0"
    color=""
  >
    Custom Class Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with custom font color 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  text-decoration-line: none;
  font-size: base;
  color: red;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color="red"
  >
    Custom Color Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with default props 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  text-decoration-line: none;
  font-size: base;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    Default Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with different sizes 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  text-decoration-line: none;
  font-size: xxsm;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    xxsm Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with different sizes 2`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 13px;
  line-height: 18px;
  font-weight: 400;
  text-decoration-line: none;
  font-size: xsm;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    xsm Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with different sizes 3`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  text-decoration-line: none;
  font-size: sm;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    sm Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with different sizes 4`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  text-decoration-line: none;
  font-size: base;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    base Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with different sizes 5`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 18px;
  line-height: 28px;
  font-weight: 400;
  text-decoration-line: none;
  font-size: mdPlus;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    mdPlus Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with different sizes 6`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 20px;
  line-height: 28px;
  font-weight: 400;
  text-decoration-line: none;
  font-size: lg;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    lg Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with different sizes 7`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 22px;
  line-height: 30px;
  font-weight: 400;
  text-decoration-line: none;
  font-size: lgPlus;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    lgPlus Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with different sizes 8`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 24px;
  line-height: 32px;
  font-weight: 400;
  text-decoration-line: none;
  font-size: xlg;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    xlg Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with different sizes 9`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 400;
  text-decoration-line: none;
  font-size: xxlg;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    xxlg Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with different weights 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 300;
  text-decoration-line: none;
  font-size: base;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    light Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with different weights 2`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  text-decoration-line: none;
  font-size: base;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    regular Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with different weights 3`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  text-decoration-line: none;
  font-size: base;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    medium Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with different weights 4`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  text-decoration-line: none;
  font-size: base;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    semiBold Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with different weights 5`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 700;
  text-decoration-line: none;
  font-size: base;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    bold Link
  </a>
</DocumentFragment>
`;

exports[`StyledRouteLink Snapshots renders correctly with underline 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  text-decoration-line: underline;
  font-size: base;
  color: #FACC15;
  cursor: pointer;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.emotion-0:visited {
  opacity: 0.8;
}

<a
    class="emotion-0"
    color=""
  >
    Underlined Link
  </a>
</DocumentFragment>
`;
