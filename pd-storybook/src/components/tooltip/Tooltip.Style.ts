import styled from '@emotion/styled';

import theme from '../../configurations/Theme.Configuration';

export type TooltipPosition = 'top' | 'right' | 'bottom' | 'left';

export interface StyledTooltipProps extends React.HtmlHTMLAttributes<HTMLDivElement> {
  position?: TooltipPosition;
  bgColor?: string;
  fontColor?: string;
}

export const TooltipContainer = styled.div`
  position: relative;
  display: inline-block;
  cursor: pointer;

  &.hovered .visible {
    visibility: visible;
    opacity: 1;
  }
`;

export const TooltipContent = styled.div<StyledTooltipProps>`
  position: absolute;
  background-color: ${(props) => props.bgColor || theme.colors.primary};
  color: ${(props) => props.fontColor || theme.colors.dark[600]};
  padding: 8px 12px;
  border-radius: ${theme.borderRadius.DEFAULT};
  font-size: ${theme.fontSize.sm};
  white-space: nowrap;
  text-wrap: wrap;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  z-index: 1000;
  width: max-content;
  text-align: left;
  max-width: 400px;
  box-shadow: ${theme.shadow.DEFAULT};



  ${({ position }) => {
    switch (position) {
      case 'top':
        return `
          bottom: 100%;
          left: 50%;
          transform: translateX(-50%);
          margin-bottom: 8px;
        `;
      case 'right':
        return `
          left: 100%;
          top: 50%;
          transform: translateY(-50%);
          margin-left: 8px;
        `;
      case 'bottom':
        return `
          top: 100%;
          left: 50%;
          transform: translateX(-50%);
          margin-top: 8px;
        `;
      case 'left':
        return `
          right: 100%;
          top: 50%;
          transform: translateY(-50%);
          margin-right: 8px;
        `;
      default:
        return '';
    }
  }}
`;
