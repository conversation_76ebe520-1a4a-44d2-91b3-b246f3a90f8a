// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Tooltip Style Snapshots TooltipContent positions renders TooltipContent with bottom position correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  background-color: #FACC15;
  color: #3A3D44;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 20px;
  white-space: nowrap;
  text-wrap: wrap;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: opacity 0.3s ease-in-out;
  transition: opacity 0.3s ease-in-out;
  z-index: 1000;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  text-align: left;
  max-width: 400px;
  box-shadow: 0 0 3px 0 rgb(0 0 0 / 0.1),0 0 2px 0 rgb(0 0 0 / 0.1);
  top: 100%;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  margin-top: 8px;
}

<div
    class="emotion-0"
  >
    bottom positioned tooltip
  </div>
</DocumentFragment>
`;

exports[`Tooltip Style Snapshots TooltipContent positions renders TooltipContent with left position correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  background-color: #FACC15;
  color: #3A3D44;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 20px;
  white-space: nowrap;
  text-wrap: wrap;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: opacity 0.3s ease-in-out;
  transition: opacity 0.3s ease-in-out;
  z-index: 1000;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  text-align: left;
  max-width: 400px;
  box-shadow: 0 0 3px 0 rgb(0 0 0 / 0.1),0 0 2px 0 rgb(0 0 0 / 0.1);
  right: 100%;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  margin-right: 8px;
}

<div
    class="emotion-0"
  >
    left positioned tooltip
  </div>
</DocumentFragment>
`;

exports[`Tooltip Style Snapshots TooltipContent positions renders TooltipContent with right position correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  background-color: #FACC15;
  color: #3A3D44;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 20px;
  white-space: nowrap;
  text-wrap: wrap;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: opacity 0.3s ease-in-out;
  transition: opacity 0.3s ease-in-out;
  z-index: 1000;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  text-align: left;
  max-width: 400px;
  box-shadow: 0 0 3px 0 rgb(0 0 0 / 0.1),0 0 2px 0 rgb(0 0 0 / 0.1);
  left: 100%;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  margin-left: 8px;
}

<div
    class="emotion-0"
  >
    right positioned tooltip
  </div>
</DocumentFragment>
`;

exports[`Tooltip Style Snapshots TooltipContent positions renders TooltipContent with top position correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  background-color: #FACC15;
  color: #3A3D44;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 20px;
  white-space: nowrap;
  text-wrap: wrap;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: opacity 0.3s ease-in-out;
  transition: opacity 0.3s ease-in-out;
  z-index: 1000;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  text-align: left;
  max-width: 400px;
  box-shadow: 0 0 3px 0 rgb(0 0 0 / 0.1),0 0 2px 0 rgb(0 0 0 / 0.1);
  bottom: 100%;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  margin-bottom: 8px;
}

<div
    class="emotion-0"
  >
    top positioned tooltip
  </div>
</DocumentFragment>
`;

exports[`Tooltip Style Snapshots renders TooltipContainer correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.emotion-0.hovered .visible {
  visibility: visible;
  opacity: 1;
}

<div
    class="emotion-0"
  >
    Tooltip Container
  </div>
</DocumentFragment>
`;

exports[`Tooltip Style Snapshots renders TooltipContainer with hovered class correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.emotion-0.hovered .visible {
  visibility: visible;
  opacity: 1;
}

<div
    class="hovered emotion-0"
  >
    Tooltip Container
  </div>
</DocumentFragment>
`;

exports[`Tooltip Style Snapshots renders TooltipContent with custom colors correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  background-color: #FF5733;
  color: #FFFFFF;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 20px;
  white-space: nowrap;
  text-wrap: wrap;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: opacity 0.3s ease-in-out;
  transition: opacity 0.3s ease-in-out;
  z-index: 1000;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  text-align: left;
  max-width: 400px;
  box-shadow: 0 0 3px 0 rgb(0 0 0 / 0.1),0 0 2px 0 rgb(0 0 0 / 0.1);
}

<div
    class="emotion-0"
  >
    Custom colored tooltip
  </div>
</DocumentFragment>
`;

exports[`Tooltip Style Snapshots renders TooltipContent with default props correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  background-color: #FACC15;
  color: #3A3D44;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 20px;
  white-space: nowrap;
  text-wrap: wrap;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: opacity 0.3s ease-in-out;
  transition: opacity 0.3s ease-in-out;
  z-index: 1000;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  text-align: left;
  max-width: 400px;
  box-shadow: 0 0 3px 0 rgb(0 0 0 / 0.1),0 0 2px 0 rgb(0 0 0 / 0.1);
}

<div
    class="emotion-0"
  >
    Default Tooltip
  </div>
</DocumentFragment>
`;
