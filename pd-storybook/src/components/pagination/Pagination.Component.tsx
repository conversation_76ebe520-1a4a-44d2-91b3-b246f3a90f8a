import React, { useEffect, useState } from 'react';

import { IconImporter } from '../iconImporter/IconImporter.Component';

import {
  PageInput,
  PageInputLabel,
  PaginationButton,
  PaginationItem,
  PaginationList,
  PaginationWrapper,
} from './Pagination.Style';

export interface PaginationProps extends React.HTMLAttributes<HTMLElement> {
  initialPage?: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  isLoading?: boolean;
}

export const Pagination: React.FC<PaginationProps> = ({
  initialPage = 1,
  totalPages,
  onPageChange,
  isLoading = false,
  ...props
}) => {
  const [pageNumber, setPageNumber] = useState(initialPage);
  const [inputValue, setInputValue] = useState(String(initialPage));

  useEffect(() => {
    setPageNumber(initialPage);
    setInputValue(String(initialPage));
  }, [initialPage]);

  const handlePageChange = (page: number) => {
    const newPage = Math.max(1, Math.min(page, totalPages));
    if (newPage !== pageNumber) {
      setPageNumber(newPage);
      onPageChange(newPage);
    }
    setInputValue(String(newPage));
  };

  const processInputValue = () => {
    const enteredPage = Number(inputValue);

    return !Number.isNaN(enteredPage) ? handlePageChange(enteredPage) : setInputValue(String(pageNumber));
  };

  const handleInputBlur = processInputValue;

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      processInputValue();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const isFirstPage = pageNumber === 1 || isLoading;
  const isLastPage = pageNumber === totalPages || isLoading;

  return (
    <PaginationWrapper {...props}>
      <PaginationList>
        <PaginationItem>
          <PaginationButton
            onClick={() => handlePageChange(1)}
            disabled={isFirstPage}
            data-testid="first-page"
          >
            <IconImporter name="caretLineLeft" />
          </PaginationButton>
        </PaginationItem>
        <PaginationItem>
          <PaginationButton
            onClick={() => handlePageChange(pageNumber - 1)}
            disabled={isFirstPage}
            data-testid="previous-page"
          >
            <IconImporter name="caretLeft" />
          </PaginationButton>
        </PaginationItem>
        <PaginationItem>
          <PageInput
            name="pageNumber"
            type="number"
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            onKeyDown={handleInputKeyDown}
            min={1}
            max={totalPages || 1}
            disabled={isLoading}
          />
          <PageInputLabel>{pageNumber} de {totalPages}</PageInputLabel>
        </PaginationItem>
        <PaginationItem>
          <PaginationButton
            onClick={() => handlePageChange(pageNumber + 1)}
            disabled={isLastPage}
            data-testid="next-page"
          >
            <IconImporter name="caretRight" />
          </PaginationButton>
        </PaginationItem>
        <PaginationItem>
          <PaginationButton
            onClick={() => handlePageChange(totalPages)}
            disabled={isLastPage}
            data-testid="last-page"
          >
            <IconImporter name="caretLineRight" />
          </PaginationButton>
        </PaginationItem>
      </PaginationList>
    </PaginationWrapper>
  );
};
