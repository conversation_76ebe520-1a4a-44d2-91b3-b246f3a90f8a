// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FileInput Style Snapshots renders StyledFileContainer correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`FileInput Style Snapshots renders StyledHidenFileInput correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: none;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`FileInput Style Snapshots renders SyledFileButton correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-radius: 8px;
  letter-spacing: 1px;
  font-weight: 300;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: none;
  font-size: 14px;
  line-height: 20px;
  padding: 7px 14px;
  background-color: #FACC15;
  color: #000000;
  position: absolute;
  right: 0;
  top: 0;
}

.emotion-0:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.emotion-0:hover:not(:disabled) {
  background-color: #FADD47;
}

<button
    class="emotion-0"
  >
    Button
  </button>
</DocumentFragment>
`;

exports[`FileInput Style Snapshots renders SyledFileButton with custom className correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-radius: 8px;
  letter-spacing: 1px;
  font-weight: 300;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: none;
  font-size: 14px;
  line-height: 20px;
  padding: 7px 14px;
  background-color: #FACC15;
  color: #000000;
  position: absolute;
  right: 0;
  top: 0;
}

.emotion-0:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.emotion-0:hover:not(:disabled) {
  background-color: #FADD47;
}

<button
    class="custom-button-class emotion-0"
  >
    Button
  </button>
</DocumentFragment>
`;
