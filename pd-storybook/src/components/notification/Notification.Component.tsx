import { ReactNode, useState } from 'react';

import OutsideClick from '../../utils/OutsideClick';
import {
  StyledHeaderNavNotificationButton,
  StyledNotificationsContainer,
} from '../header/Header.Style';
import { IconImporter } from '../iconImporter/IconImporter.Component';

import {
  NotificationItem,
} from './NotificationMenu.Component';

interface NotificationComponentProps {
  notifications?: Array<NotificationItem> | null;
  badgeCount?: number | string;
  children?: ReactNode;
}

export const NotificationComponent: React.FC<NotificationComponentProps> = ({
  notifications = [],
  badgeCount,
  children,
}) => {
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);

  const handleNotification = () => {
    if (children || (notifications && notifications?.length > 0)) {
      setIsNotificationOpen(!isNotificationOpen);
    }
  };

  const handleOutsideClick = () => {
    setIsNotificationOpen(false);
  };

  const hasContent = children || (notifications && notifications.length > 0);

  return (
    <OutsideClick onOutsideClick={handleOutsideClick}>
      <StyledNotificationsContainer>
        <StyledHeaderNavNotificationButton
          data-testid="notificationButton"
          onClick={handleNotification}
          style={{ cursor: hasContent ? 'pointer' : 'default' }}
        >
          <IconImporter name={isNotificationOpen ? 'x' : 'bell'} />
          {badgeCount !== undefined && badgeCount !== null && badgeCount !== 0 && (
            <span
              className="pd-absolute pd--top-0 pd--right-0 pd-flex pd-h-5 pd-w-5 pd-items-center
                         pd-justify-center pd-rounded-full pd-text-white pd-text-xs pd-font-semibold"
              style={{ backgroundColor: '#17448d' }}
            >
              {badgeCount}
            </span>
          )}
        </StyledHeaderNavNotificationButton>
        {isNotificationOpen && hasContent && children}
      </StyledNotificationsContainer>
    </OutsideClick>
  );
};
