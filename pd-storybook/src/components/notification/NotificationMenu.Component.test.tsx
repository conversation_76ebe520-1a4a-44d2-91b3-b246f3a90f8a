import { render, screen } from '@testing-library/react';

import { DefaultNotificationMenu, NotificationItem } from './NotificationMenu.Component';

describe('DefaultNotificationMenu', () => {
  const mockNotifications: NotificationItem[] = [
    {
      id: '1', message: 'Notification 1', isRead: false, createdAt: new Date(),
    },
    {
      id: '2', message: 'Notification 2', isRead: true, createdAt: new Date(),
    },
  ];

  test('renders notifications', () => {
    render(
      <DefaultNotificationMenu
        isOpen={true}
        notifications={mockNotifications}
        visibleNotifications={mockNotifications}
        remainingCount={0}
        showRemainingCount={false}
      />,
    );

    expect(screen.getByText('Notification 1')).toBeInTheDocument();
    expect(screen.getByText('Notification 2')).toBeInTheDocument();
  });

  test('shows remaining count', () => {
    render(
      <DefaultNotificationMenu
        isOpen={true}
        notifications={mockNotifications}
        visibleNotifications={mockNotifications}
        remainingCount={3}
        showRemainingCount={true}
      />,
    );

    expect(screen.getByText('3 more notifications')).toBeInTheDocument();
  });

  test('shows "No notifications" message when there are no notifications', () => {
    render(
      <DefaultNotificationMenu
        isOpen={true}
        notifications={[]}
        visibleNotifications={[]}
        remainingCount={0}
        showRemainingCount={false}
      />,
    );

    expect(screen.getByTestId('noNotifications')).toBeInTheDocument();
  });

  test('does not render when isOpen is false', () => {
    const { container } = render(
      <DefaultNotificationMenu
        isOpen={false}
        notifications={mockNotifications}
        visibleNotifications={mockNotifications}
        remainingCount={0}
        showRemainingCount={false}
      />,
    );

    const menu = container.firstChild;
    expect(menu).toHaveStyle('opacity: 0');
  });
});
