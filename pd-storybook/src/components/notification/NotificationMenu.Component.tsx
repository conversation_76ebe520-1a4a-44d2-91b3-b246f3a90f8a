import React from 'react';

import {
  StyledNotificationsMenuContainer,
  StyledNotificationsMenuMessages,
} from '../header/Header.Style';
import { RouteLink } from '../routeLink/RouteLink.Component';
import { Title } from '../title/Title.Component';

export interface NotificationItem {
  id: string;
  message: string;
  isRead: boolean;
  createdAt: Date;
}

export interface NotificationMenuProps {
  notifications: NotificationItem[];
  visibleNotifications: NotificationItem[];
  remainingCount: number;
  showRemainingCount: boolean;
  isOpen: boolean;
}

// Componente por defecto para el contenido del menú de notificaciones
export const DefaultNotificationMenu: React.FC<NotificationMenuProps> = ({
  visibleNotifications,
  remainingCount,
  showRemainingCount,
  isOpen,
}) => (
  <StyledNotificationsMenuContainer isOpen={isOpen}>
    <Title as="h4" size="xsm" weight="regular">
      Notifications
    </Title>
    {visibleNotifications?.length ? (
      <StyledNotificationsMenuMessages>
        {visibleNotifications.map((notification) => (
          <RouteLink
            to={''}
            className="!pd-text-dark-600 pd-py-2 pd-px-3 hover:pd-bg-slate-200 hover:!pd-text-dark-500 hover:pd-rounded"
            size="xxsm"
            weight="light"
            key={notification.id}
            >
            {notification.message}
          </RouteLink>
        ))}
        {showRemainingCount && remainingCount > 0 && (
        <div className="footer-remaining-count pd-py-2 pd-px-3 pd-text-center pd-text-xs pd-text-gray-500 pd-border-t">
          {remainingCount} more notification{remainingCount !== 1 ? 's' : ''}
        </div>
        )}
      </StyledNotificationsMenuMessages>
    ) : (
      <Title as="h5" size="xxsm" weight="light" data-testid="noNotifications">
        No notifications
      </Title>
    )}
  </StyledNotificationsMenuContainer>
);
