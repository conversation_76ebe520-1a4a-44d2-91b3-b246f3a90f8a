import { fireEvent, render, screen } from '@testing-library/react';

import { NotificationComponent } from './Notification.Component';
import { DefaultNotificationMenu, NotificationItem } from './NotificationMenu.Component';

describe('NotificationComponent', () => {
  const mockNotifications: NotificationItem[] = [
    {
      id: '1', message: 'Notification 1', isRead: false, createdAt: new Date(),
    },
    {
      id: '2', message: 'Notification 2', isRead: true, createdAt: new Date(),
    },
  ];

  test('renders notification button', () => {
    render(<NotificationComponent />);
    expect(screen.getByTestId('notificationButton')).toBeInTheDocument();
  });

  test('shows badge count', () => {
    render(<NotificationComponent badgeCount={5} />);
    expect(screen.getByText('5')).toBeInTheDocument();
  });

  test('does not show badge count when it is 0', () => {
    render(<NotificationComponent badgeCount={0} />);
    expect(screen.queryByText('0')).not.toBeInTheDocument();
  });

  test('opens notification menu on click', () => {
    render(
      <NotificationComponent notifications={mockNotifications}>
        <DefaultNotificationMenu
          isOpen={true}
          notifications={mockNotifications}
          visibleNotifications={mockNotifications}
          remainingCount={0}
          showRemainingCount={false}
        />
      </NotificationComponent>,
    );

    const notificationButton = screen.getByTestId('notificationButton');
    fireEvent.click(notificationButton);

    expect(screen.getByText('Notification 1')).toBeInTheDocument();
    expect(screen.getByText('Notification 2')).toBeInTheDocument();
  });

  test('closes notification menu on outside click', () => {
    render(
      <NotificationComponent notifications={mockNotifications}>
        <DefaultNotificationMenu
          isOpen={true}
          notifications={mockNotifications}
          visibleNotifications={mockNotifications}
          remainingCount={0}
          showRemainingCount={false}
        />
      </NotificationComponent>,
    );

    const notificationButton = screen.getByTestId('notificationButton');
    fireEvent.click(notificationButton);

    expect(screen.getByText('Notification 1')).toBeInTheDocument();

    fireEvent.mouseDown(document);

    expect(screen.queryByText('Notification 1')).not.toBeInTheDocument();
  });

  test('does not open menu if there are no notifications or children', () => {
    render(<NotificationComponent />);

    const notificationButton = screen.getByTestId('notificationButton');
    fireEvent.click(notificationButton);

    // We can't directly test if the menu is open or not,
    // but we can check that the icon is not 'x'
    expect(screen.queryByText('x')).not.toBeInTheDocument();
  });
});
