// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StyledHeader Snapshots renders StyledHeader correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  height: 5rem;
  width: 100%;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 12px;
  background-color: #F6F7FA;
}

<header
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledHeader Snapshots renders StyledHeaderLogo correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  height: 1.5rem;
  width: auto;
}

<img
    alt="Logo"
    class="emotion-0"
    src="logo.png"
  />
</DocumentFragment>
`;

exports[`StyledHeader Snapshots renders StyledHeaderLogoContainer correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-width: 150px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 6px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledHeader Snapshots renders StyledHeaderNav correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 1rem;
}

<nav
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledHeader Snapshots renders StyledHeaderNavItem correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 4px 12px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 1rem;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-width: 10rem;
  cursor: pointer;
  position: relative;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  z-index: 50;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledHeader Snapshots renders StyledHeaderNavItemText correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledHeader Snapshots renders StyledHeaderNavNotificationButton correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: transparent;
  height: 3rem;
  width: 3rem;
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  z-index: 20;
  position: relative;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}

.emotion-0:hover {
  color: #FACC15;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledHeader Snapshots renders StyledNotificationsContainer correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
  z-index: 10;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledHeader Snapshots renders StyledNotificationsMenuContainer correctly when closed 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #FFFFFF;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  padding: 1rem;
  width: 12rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 4px;
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledHeader Snapshots renders StyledNotificationsMenuContainer correctly when open 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #FFFFFF;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  padding: 1rem;
  width: 12rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 4px;
  opacity: 1;
  max-height: 20rem;
  overflow: hidden;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledHeader Snapshots renders StyledNotificationsMenuMessages correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledHeader Snapshots renders StyledProfileAvatar correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  height: 2rem;
  width: 2rem;
  border-radius: 50%;
  -webkit-background-size: cover;
  background-size: cover;
  background-image: url(avatar.jpg);
  -webkit-background-position: center;
  background-position: center;
}

<div
    class="emotion-0"
    src="avatar.jpg"
  />
</DocumentFragment>
`;

exports[`StyledHeader Snapshots renders StyledProfileSubMenu correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #FFFFFF;
  position: absolute;
  top: 90%;
  left: 0;
  right: 0;
  padding: 12px 0 8px 0;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledHeader Snapshots renders StyledProfileTextAvatar correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  height: 2rem;
  width: 2rem;
  border-radius: 50%;
  background-color: #A3A5AB;
  color: #FFFFFF;
  font-size: 20px;
  line-height: 28px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div
    class="emotion-0"
  >
    JD
  </div>
</DocumentFragment>
`;
