import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';

import { IconName } from '../iconImporter/IconMap.Component';

import { Sidebar } from './Sidebar.Component';

type SidebarSubItem = {
  id: string;
  label: string;
  to: string;
};

type SidebarItem = {
  id: string;
  label: string;
  icon: IconName;
  onClick?: () => void;
  to?: string;
  subItems?: SidebarSubItem[];
};

type SidebarGroup = {
  groupLabel: string;
  items: SidebarItem[];
};

const createTestSidebarGroups = (groups: Array<{
  groupLabel: string;
  items: Array<{
    id: string;
    label: string;
    icon: string;
    onClick?: () => void;
    to?: string;
    subItems?: Array<{ id: string; label: string; to: string }>;
  }>;
}>): SidebarGroup[] => groups.map((group) => ({
  ...group,
  items: group.items.map((item) => ({
    ...item,
    icon: item.icon as IconName,
  })),
}));

interface MockRouteLinkProps {
  to: string;
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

interface MockUserMenuItem {
  id: string;
  label: string;
  onClick: () => void;
}

interface MockProfileUserInfo {
  name: string;
}

interface MockLinkComponentProps {
  to: string;
  children: React.ReactNode;
  className?: string;
}

jest.mock('../../utils/Cn.Util', () => ({
  cn: jest.fn((...classes) => classes.join(' ')),
}));
jest.mock('../iconImporter/IconImporter.Component', () => ({
  IconImporter: ({ name, className }: { name: string; className?: string }) => (
    <span data-testid={`icon-${name}`} className={className}>{name}</span>
  ),
}));
jest.mock('../routeLink/RouteLink.Component', () => ({
  RouteLink: ({
    to, children, className, style,
  }: MockRouteLinkProps) => (
    <a href={to} className={className} style={style}>{children}</a>
  ),
}));
jest.mock('./UserSection.Component', () => ({
  UserSection: jest.fn(({ profileUserInfo, logoutButton, items }: {
    profileUserInfo?: MockProfileUserInfo;
    logoutButton?: React.ReactNode;
    items?: MockUserMenuItem[];
  }) => (
    <div data-testid="user-section">
      {profileUserInfo && <span data-testid="user-info">{profileUserInfo.name}</span>}
      {logoutButton && <div data-testid="logout-button">{logoutButton}</div>}
      {items && items.map((item: MockUserMenuItem) => <div key={item.id} data-testid={`user-menu-item-${item.id}`} onClick={item.onClick}>{item.label}</div>)}
    </div>
  )),
}));

const mockSidebarGroups: SidebarGroup[] = [
  {
    groupLabel: 'Main',
    items: [
      {
        id: 'dashboard', label: 'Dashboard', icon: 'home' as IconName, to: '/dashboard',
      },
      {
        id: 'settings', label: 'Settings', icon: 'gear' as IconName, to: '/settings',
      },
    ],
  },
  {
    groupLabel: 'Management',
    items: [
      {
        id: 'users',
        label: 'Users',
        icon: 'users' as IconName,
        to: '/users',
        subItems: [
          { id: 'user-list', label: 'List Users', to: '/users/list' },
          { id: 'user-create', label: 'Create User', to: '/users/create' },
        ],
      },
      {
        id: 'reports', label: 'Reports', icon: 'chart' as IconName, onClick: jest.fn(),
      },
    ],
  },
];

// const mockProfileUserInfo: ProfileUserInfo = { userName: 'Test User', userRol: 'Admin' };
// const mockLogoutButton: LogoutButton = { onClick: jest.fn(), children: 'Logout' };
// const mockUserMenuItems: UserMenuItem[] = [{
//   id: 'profile', label: 'Profile', icon: 'user' as IconName, onClick: jest.fn(),
// }];
const MockLinkComponent = ({ to, children, className }: MockLinkComponentProps) => <a href={to} className={className}>{children}</a>;

describe('Sidebar', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing with minimal props', () => {
    render(<Sidebar sidebarGroups={[]} path="/" />);
    expect(screen.getByRole('complementary')).toBeInTheDocument();
    expect(screen.getByTestId('user-section')).toBeInTheDocument();
  });

  it('renders sidebar groups and items', () => {
    render(<Sidebar sidebarGroups={mockSidebarGroups} path="/" />);

    expect(screen.getByText('Main')).toBeInTheDocument();
    expect(screen.getByText('Management')).toBeInTheDocument();

    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
    expect(screen.getByText('Users')).toBeInTheDocument();
    expect(screen.getByText('Reports')).toBeInTheDocument();

    expect(screen.queryByText('List Users')).not.toBeInTheDocument();
    expect(screen.queryByText('Create User')).not.toBeInTheDocument();
  });

  it('uses the provided LinkComponent if available', () => {
    render(<Sidebar sidebarGroups={mockSidebarGroups} path="/" LinkComponent={MockLinkComponent} />);

    const dashboardLink = screen.getByText('Dashboard');
    expect(dashboardLink.closest('a')).toHaveAttribute('href', '/dashboard');
  });

  it('uses RouteLink if LinkComponent is not provided', () => {
    render(<Sidebar sidebarGroups={mockSidebarGroups} path="/" />);

    const dashboardLink = screen.getByText('Dashboard');
    expect(dashboardLink.closest('a')).toHaveAttribute('href', '/dashboard');
  });

  it('applies active class to the most specific matching item', () => {
    const { rerender } = render(<Sidebar sidebarGroups={mockSidebarGroups} path="/dashboard" />);

    expect(screen.getByText('Dashboard').closest('a')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');
    expect(screen.getByText('Settings').closest('a')).not.toHaveClass('pd-bg-yellow-400');

    rerender(<Sidebar sidebarGroups={mockSidebarGroups} path="/users/list" />);

    expect(screen.getByText('Users').closest('button')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');

    fireEvent.click(screen.getByText('Users'));
    expect(screen.getByText('List Users').closest('a')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');

    rerender(<Sidebar sidebarGroups={mockSidebarGroups} path="/users" />);
    expect(screen.getByText('Users').closest('button')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');
  });

  it('opens the parent group if an active sub-item exists on initial render', () => {
    render(<Sidebar sidebarGroups={mockSidebarGroups} path="/users/create" />);

    expect(screen.getByText('List Users')).toBeInTheDocument();
    expect(screen.getByText('Create User')).toBeInTheDocument();

    expect(screen.getByText('Create User').closest('a')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');

    expect(screen.getByText('Users').closest('button')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');
  });

  it('toggles sub-item visibility when clicking a group item button', () => {
    render(<Sidebar sidebarGroups={mockSidebarGroups} path="/" />);

    const usersButton = screen.getByText('Users').closest('button')!;

    expect(screen.queryByText('List Users')).not.toBeInTheDocument();

    fireEvent.click(usersButton);
    expect(screen.getByText('List Users')).toBeInTheDocument();
    expect(screen.getByText('Create User')).toBeInTheDocument();
    expect(usersButton).toHaveAttribute('aria-expanded', 'true');
    expect(screen.getByTestId('icon-caretDown')).toHaveClass('pd-rotate-180');

    fireEvent.click(usersButton);
    expect(screen.queryByText('List Users')).not.toBeInTheDocument();
    expect(screen.queryByText('Create User')).not.toBeInTheDocument();
    expect(usersButton).toHaveAttribute('aria-expanded', 'false');
    expect(screen.getByTestId('icon-caretDown')).not.toHaveClass('pd-rotate-180');
  });

  it('calls onClick for button items', () => {
    const reportsOnClick = jest.fn();
    const groupsWithButton = [
      {
        groupLabel: 'Test',
        items: [
          {
            id: 'button-item', label: 'Click Me', icon: 'star' as IconName, onClick: reportsOnClick,
          },
        ],
      },
    ];
    render(<Sidebar sidebarGroups={groupsWithButton} path="/" />);

    const buttonItem = screen.getByText('Click Me').closest('button')!;
    fireEvent.click(buttonItem);

    expect(reportsOnClick).toHaveBeenCalledTimes(1);
  });

  it('handles items with both subItems and onClick (should prioritize subItems rendering)', () => {
    const onClickMock = jest.fn();
    const groupsWithSubItemsAndOnClick = [
      {
        groupLabel: 'Test',
        items: [
          {
            id: 'group-with-click',
            label: 'Group with Click',
            icon: 'folder' as IconName,
            onClick: onClickMock,
            subItems: [{ id: 'sub', label: 'Sub Item', to: '/sub' }],
          },
        ],
      },
    ];
    render(<Sidebar sidebarGroups={groupsWithSubItemsAndOnClick} path="/" />);

    const groupButton = screen.getByText('Group with Click').closest('button')!;

    fireEvent.click(groupButton);
    expect(screen.getByText('Sub Item')).toBeInTheDocument();
    expect(onClickMock).not.toHaveBeenCalled();

    fireEvent.click(groupButton);
    expect(screen.queryByText('Sub Item')).not.toBeInTheDocument();
    expect(onClickMock).not.toHaveBeenCalled();
  });

  it('handles items with only subItems (should behave like a group button)', () => {
    const groupsWithOnlySubItems = [
      {
        groupLabel: 'Test',
        items: [
          {
            id: 'group-only-sub',
            label: 'Group Only Sub',
            icon: 'folder' as IconName,
            subItems: [{ id: 'sub', label: 'Sub Item', to: '/sub' }],
          },
        ],
      },
    ];
    render(<Sidebar sidebarGroups={groupsWithOnlySubItems} path="/" />);

    const groupButton = screen.getByText('Group Only Sub').closest('button')!;

    fireEvent.click(groupButton);
    expect(screen.getByText('Sub Item')).toBeInTheDocument();

    fireEvent.click(groupButton);
    expect(screen.queryByText('Sub Item')).not.toBeInTheDocument();
  });

  it('handles items with no to, onClick, or subItems (should behave like a button with no action)', () => {
    const groupsWithNoAction = [
      {
        groupLabel: 'Test',
        items: [
          { id: 'no-action', label: 'No Action', icon: 'info' as IconName },
        ],
      },
    ];
    render(<Sidebar sidebarGroups={groupsWithNoAction} path="/" />);

    const noActionButton = screen.getByText('No Action').closest('button')!;

    fireEvent.click(noActionButton);
  });

  it('handles empty sidebarGroups array', () => {
    render(<Sidebar sidebarGroups={[]} path="/" />);
    expect(screen.queryByText('Main')).not.toBeInTheDocument();
    expect(screen.queryByText('Management')).not.toBeInTheDocument();
  });

  it('handles sidebarGroups with empty items array', () => {
    const groupsWithEmptyItems = [{ groupLabel: 'Empty', items: [] }];
    render(<Sidebar sidebarGroups={groupsWithEmptyItems} path="/" />);
    expect(screen.getByText('Empty')).toBeInTheDocument();

    const groupDiv = screen.getByText('Empty').parentElement;
    expect(groupDiv?.querySelector('nav ul')).toBeEmptyDOMElement();
  });

  it('applies correct classes to sub-items based on active state', () => {
    render(<Sidebar sidebarGroups={mockSidebarGroups} path="/users/create" />);

    if (!screen.queryByText('Create User')) {
      fireEvent.click(screen.getByText('Users'));
    }

    expect(screen.getByText('Create User').closest('a')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');
    expect(screen.getByText('List Users').closest('a')).toHaveClass('!pd-text-gray-100 hover:pd-bg-gray-500/50 hover:pd-text-yellow-400');
  });

  it('applies correct classes to items based on active state', () => {
    render(<Sidebar sidebarGroups={mockSidebarGroups} path="/settings" />);

    expect(screen.getByText('Settings').closest('a')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');
    expect(screen.getByText('Dashboard').closest('a')).toHaveClass('!pd-text-gray-100 hover:pd-bg-gray-500/50 hover:pd-text-yellow-400');

    expect(screen.getByText('Users').closest('button')).toHaveClass('!pd-text-gray-100 hover:pd-bg-gray-500/50 hover:pd-text-yellow-400');
  });

  it('correctly identifies the active item when paths are nested', () => {
    const nestedGroups = [
      {
        groupLabel: 'Nested',
        items: [
          {
            id: 'parent', label: 'Parent', icon: 'folder' as IconName, to: '/parent',
          },
          {
            id: 'nested-group',
            label: 'Nested Group',
            icon: 'folder' as IconName,
            to: '/parent/child',
            subItems: [
              { id: 'child', label: 'Child', to: '/parent/child/list' },
              { id: 'grandchild', label: 'Grandchild', to: '/parent/child/grandchild' },
            ],
          },
        ],
      },
    ];

    const { rerender } = render(<Sidebar sidebarGroups={nestedGroups} path="/parent/child/grandchild" />);

    if (!screen.queryByText('Grandchild')) {
      fireEvent.click(screen.getByText('Nested Group'));
    }

    expect(screen.getByText('Grandchild').closest('a')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');
    expect(screen.getByText('Child').closest('a')).not.toHaveClass('pd-bg-yellow-400');

    expect(screen.getByText('Nested Group').closest('button')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');

    expect(screen.getByText('Parent').closest('a')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');

    rerender(<Sidebar sidebarGroups={nestedGroups} path="/parent/child/list" />);

    if (!screen.queryByText('Child')) {
      fireEvent.click(screen.getByText('Nested Group'));
    }

    expect(screen.getByText('Child').closest('a')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');
    expect(screen.getByText('Grandchild').closest('a')).not.toHaveClass('pd-bg-yellow-400');

    expect(screen.getByText('Nested Group').closest('button')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');

    expect(screen.getByText('Parent').closest('a')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');

    rerender(<Sidebar sidebarGroups={nestedGroups} path="/parent" />);

    expect(screen.getByText('Parent').closest('a')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');

    expect(screen.getByText('Nested Group').closest('button')).not.toHaveClass('pd-bg-yellow-400');
  });

  it('does not open any group initially if no active sub-item exists', () => {
    render(<Sidebar sidebarGroups={mockSidebarGroups} path="/some/other/path" />);

    expect(screen.queryByText('List Users')).not.toBeInTheDocument();
    expect(screen.queryByText('Create User')).not.toBeInTheDocument();

    expect(screen.getByText('Dashboard').closest('a')).not.toHaveClass('pd-bg-yellow-400');
    expect(screen.getByText('Settings').closest('a')).not.toHaveClass('pd-bg-yellow-400');
    expect(screen.getByText('Users').closest('button')).not.toHaveClass('pd-bg-yellow-400');
  });

  it('handles items with onClick and no icon', () => {
    const groups = [
      {
        groupLabel: 'Test',
        items: [
          {
            id: 'no-icon-button', label: 'No Icon Button', icon: 'info' as IconName, onClick: jest.fn(),
          },
        ],
      },
    ];
    render(<Sidebar sidebarGroups={groups} path="/" />);
    expect(screen.getByText('No Icon Button')).toBeInTheDocument();
  });

  it('handles items with to and no icon', () => {
    const groups = [
      {
        groupLabel: 'Test',
        items: [
          {
            id: 'no-icon-link', label: 'No Icon Link', icon: 'info' as IconName, to: '/no-icon',
          },
        ],
      },
    ];
    render(<Sidebar sidebarGroups={groups} path="/" />);
    const link = screen.getByText('No Icon Link').closest('a');
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute('href', '/no-icon');
  });

  it('handles sub-items with no icon (not applicable based on type definition, but good to consider)', () => {

  });

  it('applies className prop to the aside element', () => {
    render(<Sidebar sidebarGroups={[]} path="/" className="custom-sidebar-class" />);
    expect(screen.getByRole('complementary')).toHaveClass('custom-sidebar-class');
  });

  it('applies style prop to RouteLink sub-items', () => {
    render(<Sidebar sidebarGroups={mockSidebarGroups} path="/users/list" />);

    if (!screen.queryByText('List Users')) {
      fireEvent.click(screen.getByText('Users'));
    }
    const subItemLink = screen.getByText('List Users').closest('a');
    expect(subItemLink).toHaveStyle('font-size: 12px');
  });

  it('does not apply style prop to LinkComponent sub-items', () => {
    render(<Sidebar sidebarGroups={mockSidebarGroups} path="/users/list" LinkComponent={MockLinkComponent} />);

    if (!screen.queryByText('List Users')) {
      fireEvent.click(screen.getByText('Users'));
    }
    const subItemLink = screen.getByText('List Users').closest('a');
    expect(subItemLink).not.toHaveStyle('font-size: 12px');
  });

  it('handles path matching a parent item with subitems, but no subitem matches', () => {
    render(<Sidebar sidebarGroups={mockSidebarGroups} path="/users" />);

    expect(screen.getByText('Users').closest('button')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');

    expect(screen.queryByText('List Users')).not.toBeInTheDocument();

    fireEvent.click(screen.getByText('Users'));
    expect(screen.getByText('List Users')).toBeInTheDocument();
  });

  it('applies active class to link items when using LinkComponent', () => {
    render(<Sidebar sidebarGroups={mockSidebarGroups} path="/dashboard" LinkComponent={MockLinkComponent} />);

    expect(screen.getByText('Dashboard').closest('a')).toHaveClass('pd-bg-yellow-400 pd-text-gray-700 pd-font-bold');
    expect(screen.getByText('Settings').closest('a')).not.toHaveClass('pd-bg-yellow-400');
  });

  it('handles button items with subItems and onClick (default button case)', () => {
    const onClickMock = jest.fn();
    const groupsWithButtonAndSubItems = [
      {
        groupLabel: 'Test',
        items: [
          {
            id: 'button-with-subitems',
            label: 'Button with SubItems',
            icon: 'folder' as IconName,
            onClick: onClickMock,
            subItems: [{ id: 'sub1', label: 'Sub Item 1', to: '/sub1' }],
          },
        ],
      },
    ];
    render(<Sidebar sidebarGroups={groupsWithButtonAndSubItems} path="/" />);

    const buttonItem = screen.getByText('Button with SubItems').closest('button')!;

    expect(screen.getByTestId('icon-caretDown')).toBeInTheDocument();

    fireEvent.click(buttonItem);
    expect(screen.getByText('Sub Item 1')).toBeInTheDocument();
    expect(onClickMock).not.toHaveBeenCalled();

    fireEvent.click(buttonItem);
    expect(screen.queryByText('Sub Item 1')).not.toBeInTheDocument();
  });

  it('handles button items with only onClick (no subItems)', () => {
    const onClickMock = jest.fn();
    const groupsWithOnlyOnClick = createTestSidebarGroups([
      {
        groupLabel: 'Test',
        items: [
          {
            id: 'button-only-onclick',
            label: 'Button Only OnClick',
            icon: 'star',
            onClick: onClickMock,
          },
        ],
      },
    ]);
    render(<Sidebar sidebarGroups={groupsWithOnlyOnClick} path="/" />);

    const buttonItem = screen.getByText('Button Only OnClick').closest('button')!;

    expect(screen.queryByTestId('icon-caretDown')).not.toBeInTheDocument();

    fireEvent.click(buttonItem);
    expect(onClickMock).toHaveBeenCalledTimes(1);
  });
});
