import styled from '@emotion/styled';

import theme from '../../configurations/Theme.Configuration';

export type ButtonVariants = 'primary' | 'outlined';
export type ButtonSizes = 'small' | 'medium' | 'large';

export interface StyledButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariants;
  size?: ButtonSizes;
}

export const StyledButton = styled.button<StyledButtonProps>`
  border-radius: ${theme.borderRadius.DEFAULT};
  letter-spacing: 1px;
  font-weight: ${theme.fontWeight.light};
  height: fit-content;
  width: fit-content;

  transition: all 0.3s ease;
  border: none;
  font-size: ${theme.fontSize.sm};

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  ${(props) => `
    padding: ${theme.buttonSize[props.size || 'small'].padding};
  `}

  ${(props) => (props.variant || 'primary') === 'primary'
    && `
      background-color: ${theme.colors.primary};
      color: ${theme.colors.dark[900]};
      &:hover:not(:disabled) {
        background-color: ${theme.colors.primaryHover};
      }
    `}

  ${(props) => props.variant === 'outlined'
    && `
      background-color: ${theme.colors.transparent};
      color: ${theme.colors.dark[900]};
      border: 1px solid ${theme.colors.secondary};
      &:hover:not(:disabled) {
        background-color: ${theme.colors.primary};
        color: ${theme.colors.dark[900]};
      }
    `}
`;
